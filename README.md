# Fleet Management Digital Twins with AI - AWS Implementation

This repository contains a comprehensive fleet management digital twins solution built for AWS. The solution leverages AWS IoT TwinMaker, SageMaker, and other AWS services to provide predictive maintenance, anomaly detection, EV charge optimization, and comprehensive fleet analytics.

## AWS Architecture Overview

The solution is built on AWS cloud platform with the following key components:

- **AWS IoT TwinMaker**: Digital twin modeling and visualization
- **AWS IoT Core**: Device connectivity and data ingestion
- **Amazon SageMaker**: AI/ML model training and inference
- **Amazon Kinesis**: Real-time data streaming and processing
- **Amazon Timestream**: Time-series data storage
- **AWS Lambda**: Serverless data processing and AI inference
- **Amazon S3**: Data lake and model storage
- **Amazon QuickSight**: Visualization and dashboards

The digital twins architecture is designed with the following hierarchy:

```
Fleet (Root)
├── Vehicles (ICE, EV, Hybrid)
├── Drivers
├── Routes
├── Charging Stations
├── Fuel Stations
├── Depots/Warehouses
├── Maintenance Schedules
└── AI/Analytics Models
    ├── Predictive Maintenance
    ├── Anomaly Detection
    ├── EV Charge Optimization
    ├── Route Optimization
    ├── Driver Behavior Analysis
    └── Fleet Analytics
```

## Key Features

- **Real-time Telemetry**: Vehicle location, speed, fuel/battery levels, engine diagnostics
- **Predictive Maintenance**: AI-powered maintenance scheduling and failure prediction
- **EV Management**: Battery optimization, charging scheduling, range prediction
- **Route Optimization**: AI-driven route planning and traffic optimization
- **Driver Analytics**: Behavior analysis, safety scoring, performance metrics
- **Anomaly Detection**: Real-time detection of unusual patterns and potential issues
- **Cost Optimization**: Fuel efficiency, maintenance cost reduction, operational optimization

## DTDL Models

### Core Models
- `Fleet.json` - Main fleet management twin
- `Vehicle.json` - Base vehicle model with specializations
- `ElectricVehicle.json` - EV-specific model extending Vehicle
- `Driver.json` - Driver behavior and performance model
- `Route.json` - Route planning and optimization model
- `ChargingStation.json` - EV charging infrastructure model
- `FuelStation.json` - Fuel infrastructure model
- `Depot.json` - Fleet depot/warehouse model

### AI/Analytics Models
- `PredictiveMaintenanceModel.json` - ML model for maintenance predictions
- `AnomalyDetectionModel.json` - Anomaly detection capabilities
- `EVChargeOptimizer.json` - EV charging optimization
- `RouteOptimizer.json` - Route planning optimization
- `DriverBehaviorAnalyzer.json` - Driver performance analysis
- `FleetAnalytics.json` - Overall fleet analytics

## Quick Start - AWS Deployment

### Prerequisites
- AWS CLI configured with appropriate permissions
- Terraform >= 1.0 installed (recommended) OR AWS CDK installed
- Python 3.8+ installed
- Docker installed (for SageMaker)

### Option 1: Terraform Deployment (Recommended)
```bash
git clone <repository-url>
cd fleet-management-digital-twins

# Make deployment script executable
chmod +x deploy-terraform.sh

# Deploy the complete solution
./deploy-terraform.sh
```

### Option 2: Manual Terraform Deployment
```bash
cd terraform

# Copy and customize variables
cp terraform.tfvars.example terraform.tfvars
vim terraform.tfvars

# Initialize and deploy
terraform init
terraform workspace new dev
terraform plan -out=tfplan
terraform apply tfplan
```

### Option 3: CDK Deployment (Alternative)
```bash
cd aws-cdk
pip install -r requirements.txt
cdk bootstrap
cdk deploy
```

#### Create IoT TwinMaker Workspace
```bash
aws iottwinmaker create-workspace \
    --workspace-id fleet-management-workspace \
    --description "Fleet Management Digital Twins" \
    --s3-location s3://fleet-twinmaker-workspace-ACCOUNT-ID \
    --role arn:aws:iam::ACCOUNT-ID:role/TwinMakerExecutionRole
```

#### Deploy Lambda Functions
```bash
# Package and deploy predictive maintenance
cd aws-lambda/predictive-maintenance
zip -r function.zip .
aws lambda create-function \
    --function-name fleet-predictive-maintenance \
    --runtime python3.9 \
    --role arn:aws:iam::ACCOUNT-ID:role/LambdaExecutionRole \
    --handler lambda_function.lambda_handler \
    --zip-file fileb://function.zip
```

### 3. Test the Deployment
```bash
# Run vehicle simulator
cd aws-testing
python3 vehicle_simulator.py --vehicles 5 --duration 60
```

## AWS Services Integration

The solution integrates with the following AWS services:
- **AWS IoT TwinMaker** for digital twin modeling and visualization
- **Amazon SageMaker** for AI/ML model training and inference
- **Amazon Kinesis** for real-time data streaming
- **Amazon Timestream** for time-series data storage
- **AWS Lambda** for serverless data processing
- **Amazon S3** for data lake and model storage
- **AWS IoT Core** for device connectivity
- **Amazon QuickSight** for visualization and dashboards

## File Structure

```
├── models/                          # DTDL model definitions
│   ├── Fleet.json
│   ├── Vehicle.json
│   ├── ElectricVehicle.json
│   └── ...
├── terraform/                       # Terraform deployment (recommended)
│   ├── main.tf
│   ├── variables.tf
│   ├── outputs.tf
│   ├── iam.tf
│   ├── lambda.tf
│   ├── iot.tf
│   ├── kinesis.tf
│   ├── data-catalog.tf
│   ├── sagemaker.tf
│   └── terraform.tfvars.example
├── aws-config/                      # AWS-specific configurations
│   ├── vehicle-properties.json
│   ├── iot-device-policy.json
│   └── fleet-management-infrastructure.yaml
├── aws-lambda/                      # Lambda function code
│   ├── predictive-maintenance/
│   ├── anomaly-detection/
│   └── telemetry-processor/
├── aws-cdk/                         # CDK deployment (alternative)
│   ├── fleet_management_stack.py
│   └── requirements.txt
├── sagemaker/                       # SageMaker training code
│   └── predictive-maintenance/
├── aws-testing/                     # Testing utilities
│   └── vehicle_simulator.py
├── deployment/                      # Deployment guides
│   └── DeploymentGuide.md
├── config/                          # Configuration files
│   └── AIModelConfiguration.json
├── deploy-terraform.sh              # Terraform deployment script
└── deploy-aws.sh                    # CDK deployment script
```

## Monitoring and Operations

After deployment, you can monitor the solution using:

- **CloudWatch Dashboards**: Monitor Lambda functions, Kinesis streams, and Timestream
- **IoT Device Management**: Track device connectivity and data flow
- **SageMaker Model Monitoring**: Monitor model performance and drift
- **TwinMaker Scenes**: Visualize digital twins in 3D environments

## Cost Optimization

The solution includes several cost optimization features:
- **Timestream data retention policies** for automatic data lifecycle management
- **S3 lifecycle rules** for transitioning data to cheaper storage classes
- **Lambda concurrency limits** to control compute costs
- **Kinesis shard scaling** based on data volume

## Security

Security features include:
- **IAM roles and policies** for least-privilege access
- **VPC endpoints** for private connectivity
- **Encryption at rest and in transit** for all data
- **IoT device certificates** for secure device authentication
