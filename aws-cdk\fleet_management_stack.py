#!/usr/bin/env python3

import aws_cdk as cdk
from aws_cdk import (
    Stack,
    aws_iot as iot,
    aws_s3 as s3,
    aws_kinesis as kinesis,
    aws_kinesisfirehose as firehose,
    aws_lambda as lambda_,
    aws_iam as iam,
    aws_timestream as timestream,
    aws_sagemaker as sagemaker,
    aws_sns as sns,
    aws_events as events,
    aws_events_targets as targets,
    Duration,
    RemovalPolicy
)
from constructs import Construct

class FleetManagementStack(Stack):
    """
    AWS CDK Stack for Fleet Management Digital Twins Infrastructure
    """

    def __init__(self, scope: Construct, construct_id: str, **kwargs) -> None:
        super().__init__(scope, construct_id, **kwargs)

        # S3 Buckets
        self.create_s3_buckets()
        
        # Timestream Database
        self.create_timestream_database()
        
        # Kinesis Streams
        self.create_kinesis_streams()
        
        # IoT Core Resources
        self.create_iot_resources()
        
        # Lambda Functions
        self.create_lambda_functions()
        
        # SageMaker Resources
        self.create_sagemaker_resources()
        
        # SNS Topics
        self.create_sns_topics()
        
        # EventBridge Rules
        self.create_eventbridge_rules()

    def create_s3_buckets(self):
        """Create S3 buckets for data storage"""
        
        # TwinMaker workspace bucket
        self.twinmaker_bucket = s3.Bucket(
            self, "TwinMakerBucket",
            bucket_name=f"fleet-twinmaker-workspace-{self.account}",
            versioned=True,
            encryption=s3.BucketEncryption.S3_MANAGED,
            block_public_access=s3.BlockPublicAccess.BLOCK_ALL,
            removal_policy=RemovalPolicy.DESTROY,
            lifecycle_rules=[
                s3.LifecycleRule(
                    id="TransitionToIA",
                    enabled=True,
                    transitions=[
                        s3.Transition(
                            storage_class=s3.StorageClass.INFREQUENT_ACCESS,
                            transition_after=Duration.days(30)
                        )
                    ]
                )
            ]
        )
        
        # Data lake bucket
        self.data_lake_bucket = s3.Bucket(
            self, "DataLakeBucket",
            bucket_name=f"fleet-data-lake-{self.account}",
            versioned=True,
            encryption=s3.BucketEncryption.S3_MANAGED,
            block_public_access=s3.BlockPublicAccess.BLOCK_ALL,
            removal_policy=RemovalPolicy.DESTROY,
            lifecycle_rules=[
                s3.LifecycleRule(
                    id="DataLifecycle",
                    enabled=True,
                    transitions=[
                        s3.Transition(
                            storage_class=s3.StorageClass.INFREQUENT_ACCESS,
                            transition_after=Duration.days(30)
                        ),
                        s3.Transition(
                            storage_class=s3.StorageClass.GLACIER,
                            transition_after=Duration.days(90)
                        )
                    ]
                )
            ]
        )

    def create_timestream_database(self):
        """Create Timestream database and tables"""
        
        # Timestream database
        self.timestream_db = timestream.CfnDatabase(
            self, "TimestreamDatabase",
            database_name="FleetManagementDB"
        )
        
        # Vehicle telemetry table
        self.vehicle_telemetry_table = timestream.CfnTable(
            self, "VehicleTelemetryTable",
            database_name=self.timestream_db.database_name,
            table_name="VehicleTelemetry",
            retention_properties={
                "MemoryStoreRetentionPeriodInHours": "24",
                "MagneticStoreRetentionPeriodInDays": "365"
            }
        )
        self.vehicle_telemetry_table.add_dependency(self.timestream_db)
        
        # Driver behavior table
        self.driver_behavior_table = timestream.CfnTable(
            self, "DriverBehaviorTable",
            database_name=self.timestream_db.database_name,
            table_name="DriverBehavior",
            retention_properties={
                "MemoryStoreRetentionPeriodInHours": "24",
                "MagneticStoreRetentionPeriodInDays": "365"
            }
        )
        self.driver_behavior_table.add_dependency(self.timestream_db)
        
        # Charging data table
        self.charging_data_table = timestream.CfnTable(
            self, "ChargingDataTable",
            database_name=self.timestream_db.database_name,
            table_name="ChargingData",
            retention_properties={
                "MemoryStoreRetentionPeriodInHours": "24",
                "MagneticStoreRetentionPeriodInDays": "365"
            }
        )
        self.charging_data_table.add_dependency(self.timestream_db)

    def create_kinesis_streams(self):
        """Create Kinesis streams for data ingestion"""
        
        # Vehicle telemetry stream
        self.vehicle_telemetry_stream = kinesis.Stream(
            self, "VehicleTelemetryStream",
            stream_name="fleet-vehicle-telemetry",
            shard_count=5,
            encryption=kinesis.StreamEncryption.KMS
        )
        
        # Driver behavior stream
        self.driver_behavior_stream = kinesis.Stream(
            self, "DriverBehaviorStream",
            stream_name="fleet-driver-behavior",
            shard_count=2,
            encryption=kinesis.StreamEncryption.KMS
        )
        
        # Charging data stream
        self.charging_data_stream = kinesis.Stream(
            self, "ChargingDataStream",
            stream_name="fleet-charging-data",
            shard_count=2,
            encryption=kinesis.StreamEncryption.KMS
        )

    def create_iot_resources(self):
        """Create IoT Core resources"""
        
        # IoT Thing Types
        self.vehicle_thing_type = iot.CfnThingType(
            self, "VehicleThingType",
            thing_type_name="FleetVehicle",
            thing_type_description="Fleet vehicle IoT thing type"
        )
        
        self.charging_station_thing_type = iot.CfnThingType(
            self, "ChargingStationThingType",
            thing_type_name="ChargingStation",
            thing_type_description="EV charging station IoT thing type"
        )
        
        # IoT Policy
        self.device_policy = iot.CfnPolicy(
            self, "FleetDevicePolicy",
            policy_name="FleetDevicePolicy",
            policy_document={
                "Version": "2012-10-17",
                "Statement": [
                    {
                        "Effect": "Allow",
                        "Action": ["iot:Connect"],
                        "Resource": ["arn:aws:iot:*:*:client/${iot:Connection.Thing.ThingName}"]
                    },
                    {
                        "Effect": "Allow",
                        "Action": ["iot:Publish"],
                        "Resource": [
                            "arn:aws:iot:*:*:topic/fleet/vehicles/${iot:Connection.Thing.ThingName}/*",
                            "arn:aws:iot:*:*:topic/fleet/drivers/${iot:Connection.Thing.ThingName}/*",
                            "arn:aws:iot:*:*:topic/fleet/charging-stations/${iot:Connection.Thing.ThingName}/*"
                        ]
                    },
                    {
                        "Effect": "Allow",
                        "Action": ["iot:Subscribe", "iot:Receive"],
                        "Resource": [
                            "arn:aws:iot:*:*:topicfilter/fleet/*/commands/*",
                            "arn:aws:iot:*:*:topic/fleet/*/commands/*"
                        ]
                    },
                    {
                        "Effect": "Allow",
                        "Action": ["iot:UpdateThingShadow", "iot:GetThingShadow"],
                        "Resource": ["arn:aws:iot:*:*:thing/${iot:Connection.Thing.ThingName}"]
                    }
                ]
            }
        )
        
        # IoT Rule Role
        self.iot_rule_role = iam.Role(
            self, "IoTRuleRole",
            assumed_by=iam.ServicePrincipal("iot.amazonaws.com"),
            inline_policies={
                "KinesisAccess": iam.PolicyDocument(
                    statements=[
                        iam.PolicyStatement(
                            effect=iam.Effect.ALLOW,
                            actions=["kinesis:PutRecord", "kinesis:PutRecords"],
                            resources=[
                                self.vehicle_telemetry_stream.stream_arn,
                                self.driver_behavior_stream.stream_arn,
                                self.charging_data_stream.stream_arn
                            ]
                        )
                    ]
                )
            }
        )
        
        # IoT Rules
        self.vehicle_telemetry_rule = iot.CfnTopicRule(
            self, "VehicleTelemetryRule",
            rule_name="FleetVehicleTelemetryRule",
            topic_rule_payload=iot.CfnTopicRule.TopicRulePayloadProperty(
                sql="SELECT * FROM 'fleet/vehicles/+/telemetry'",
                rule_disabled=False,
                actions=[
                    iot.CfnTopicRule.ActionProperty(
                        kinesis=iot.CfnTopicRule.KinesisActionProperty(
                            stream_name=self.vehicle_telemetry_stream.stream_name,
                            partition_key="${vehicleId}",
                            role_arn=self.iot_rule_role.role_arn
                        )
                    )
                ]
            )
        )

    def create_lambda_functions(self):
        """Create Lambda functions for data processing and AI inference"""
        
        # Lambda execution role
        self.lambda_role = iam.Role(
            self, "LambdaExecutionRole",
            assumed_by=iam.ServicePrincipal("lambda.amazonaws.com"),
            managed_policies=[
                iam.ManagedPolicy.from_aws_managed_policy_name("service-role/AWSLambdaBasicExecutionRole")
            ],
            inline_policies={
                "TimestreamAccess": iam.PolicyDocument(
                    statements=[
                        iam.PolicyStatement(
                            effect=iam.Effect.ALLOW,
                            actions=[
                                "timestream:WriteRecords",
                                "timestream:DescribeEndpoints",
                                "timestream:Select",
                                "timestream:DescribeTable",
                                "timestream:ListTables"
                            ],
                            resources=["*"]
                        )
                    ]
                ),
                "SageMakerAccess": iam.PolicyDocument(
                    statements=[
                        iam.PolicyStatement(
                            effect=iam.Effect.ALLOW,
                            actions=["sagemaker:InvokeEndpoint"],
                            resources=["*"]
                        )
                    ]
                ),
                "S3Access": iam.PolicyDocument(
                    statements=[
                        iam.PolicyStatement(
                            effect=iam.Effect.ALLOW,
                            actions=["s3:GetObject", "s3:PutObject"],
                            resources=[
                                f"{self.data_lake_bucket.bucket_arn}/*",
                                f"{self.twinmaker_bucket.bucket_arn}/*"
                            ]
                        )
                    ]
                )
            }
        )
        
        # Telemetry processor function
        self.telemetry_processor = lambda_.Function(
            self, "TelemetryProcessor",
            function_name="fleet-telemetry-processor",
            runtime=lambda_.Runtime.PYTHON_3_9,
            handler="lambda_function.lambda_handler",
            code=lambda_.Code.from_asset("aws-lambda/telemetry-processor"),
            timeout=Duration.minutes(5),
            memory_size=512,
            role=self.lambda_role,
            environment={
                "TIMESTREAM_DATABASE": self.timestream_db.database_name,
                "VEHICLE_TABLE": self.vehicle_telemetry_table.table_name,
                "DRIVER_TABLE": self.driver_behavior_table.table_name,
                "CHARGING_TABLE": self.charging_data_table.table_name
            }
        )
        
        # Predictive maintenance function
        self.predictive_maintenance_function = lambda_.Function(
            self, "PredictiveMaintenanceFunction",
            function_name="fleet-predictive-maintenance",
            runtime=lambda_.Runtime.PYTHON_3_9,
            handler="lambda_function.lambda_handler",
            code=lambda_.Code.from_asset("aws-lambda/predictive-maintenance"),
            timeout=Duration.minutes(5),
            memory_size=512,
            role=self.lambda_role,
            environment={
                "TIMESTREAM_DATABASE": self.timestream_db.database_name,
                "TIMESTREAM_TABLE": self.vehicle_telemetry_table.table_name,
                "SAGEMAKER_ENDPOINT": "predictive-maintenance-endpoint"
            }
        )
        
        # Anomaly detection function
        self.anomaly_detection_function = lambda_.Function(
            self, "AnomalyDetectionFunction",
            function_name="fleet-anomaly-detection",
            runtime=lambda_.Runtime.PYTHON_3_9,
            handler="lambda_function.lambda_handler",
            code=lambda_.Code.from_asset("aws-lambda/anomaly-detection"),
            timeout=Duration.minutes(5),
            memory_size=512,
            role=self.lambda_role,
            environment={
                "TIMESTREAM_DATABASE": self.timestream_db.database_name,
                "TIMESTREAM_TABLE": self.vehicle_telemetry_table.table_name,
                "MODEL_S3_BUCKET": self.data_lake_bucket.bucket_name
            }
        )

    def create_sagemaker_resources(self):
        """Create SageMaker resources"""
        
        # SageMaker execution role
        self.sagemaker_role = iam.Role(
            self, "SageMakerExecutionRole",
            assumed_by=iam.ServicePrincipal("sagemaker.amazonaws.com"),
            managed_policies=[
                iam.ManagedPolicy.from_aws_managed_policy_name("AmazonSageMakerFullAccess")
            ],
            inline_policies={
                "S3Access": iam.PolicyDocument(
                    statements=[
                        iam.PolicyStatement(
                            effect=iam.Effect.ALLOW,
                            actions=["s3:*"],
                            resources=[
                                self.data_lake_bucket.bucket_arn,
                                f"{self.data_lake_bucket.bucket_arn}/*"
                            ]
                        )
                    ]
                )
            }
        )

    def create_sns_topics(self):
        """Create SNS topics for alerts"""
        
        self.maintenance_alerts_topic = sns.Topic(
            self, "MaintenanceAlertsTopic",
            topic_name="fleet-maintenance-alerts",
            display_name="Fleet Maintenance Alerts"
        )
        
        self.anomaly_alerts_topic = sns.Topic(
            self, "AnomalyAlertsTopic",
            topic_name="fleet-anomaly-alerts",
            display_name="Fleet Anomaly Alerts"
        )

    def create_eventbridge_rules(self):
        """Create EventBridge rules for scheduled tasks"""
        
        # Daily analytics rule
        daily_analytics_rule = events.Rule(
            self, "DailyAnalyticsRule",
            schedule=events.Schedule.cron(hour="2", minute="0"),
            description="Trigger daily fleet analytics processing"
        )
        
        daily_analytics_rule.add_target(
            targets.LambdaFunction(self.predictive_maintenance_function)
        )
        
        # Weekly model retraining rule
        weekly_retrain_rule = events.Rule(
            self, "WeeklyRetrainRule",
            schedule=events.Schedule.cron(hour="1", minute="0", week_day="MON"),
            description="Trigger weekly model retraining"
        )

    def add_outputs(self):
        """Add CloudFormation outputs"""
        
        cdk.CfnOutput(
            self, "TwinMakerBucketName",
            value=self.twinmaker_bucket.bucket_name,
            description="S3 bucket for TwinMaker workspace"
        )
        
        cdk.CfnOutput(
            self, "DataLakeBucketName",
            value=self.data_lake_bucket.bucket_name,
            description="S3 bucket for data lake"
        )
        
        cdk.CfnOutput(
            self, "TimestreamDatabaseName",
            value=self.timestream_db.database_name,
            description="Timestream database name"
        )
        
        cdk.CfnOutput(
            self, "VehicleTelemetryStreamName",
            value=self.vehicle_telemetry_stream.stream_name,
            description="Kinesis stream for vehicle telemetry"
        )


# CDK App
app = cdk.App()
FleetManagementStack(app, "FleetManagementStack")
app.synth()
