{"driverId": {"dataType": {"type": "STRING"}, "isRequiredInEntity": true, "isStoredExternally": false, "isTimeSeries": false}, "employeeId": {"dataType": {"type": "STRING"}, "isRequiredInEntity": true, "isStoredExternally": false, "isTimeSeries": false}, "firstName": {"dataType": {"type": "STRING"}, "isRequiredInEntity": true, "isStoredExternally": false, "isTimeSeries": false}, "lastName": {"dataType": {"type": "STRING"}, "isRequiredInEntity": true, "isStoredExternally": false, "isTimeSeries": false}, "licenseNumber": {"dataType": {"type": "STRING"}, "isRequiredInEntity": true, "isStoredExternally": false, "isTimeSeries": false}, "licenseClass": {"dataType": {"type": "STRING", "allowedValues": ["class_a", "class_b", "class_c", "motorcycle"]}, "isRequiredInEntity": true, "isStoredExternally": false, "isTimeSeries": false}, "experienceYears": {"dataType": {"type": "INTEGER"}, "isRequiredInEntity": true, "isStoredExternally": false, "isTimeSeries": false}, "status": {"dataType": {"type": "STRING", "allowedValues": ["active", "on_leave", "training", "suspended", "inactive"]}, "isRequiredInEntity": true, "isStoredExternally": false, "isTimeSeries": false, "defaultValue": {"stringValue": "inactive"}}, "drivingBehavior": {"dataType": {"type": "MAP", "nestedType": {"type": "DOUBLE"}}, "isRequiredInEntity": false, "isStoredExternally": true, "isTimeSeries": true, "dataTypeSpec": {"averageSpeed": "DOUBLE", "maxSpeed": "DOUBLE", "harshAccelerationEvents": "INTEGER", "harshBrakingEvents": "INTEGER", "sharpTurnEvents": "INTEGER", "speedingEvents": "INTEGER", "idlingTime": "DOUBLE", "fuelEfficiencyScore": "DOUBLE", "safetyScore": "DOUBLE", "ecoScore": "DOUBLE"}}, "workingHours": {"dataType": {"type": "MAP", "nestedType": {"type": "STRING"}}, "isRequiredInEntity": false, "isStoredExternally": true, "isTimeSeries": true, "dataTypeSpec": {"shiftStartTime": "STRING", "shiftEndTime": "STRING", "totalDrivingTime": "DOUBLE", "breakTime": "DOUBLE", "overtimeHours": "DOUBLE", "complianceStatus": "STRING"}}, "performanceMetrics": {"dataType": {"type": "MAP", "nestedType": {"type": "DOUBLE"}}, "isRequiredInEntity": false, "isStoredExternally": true, "isTimeSeries": true, "dataTypeSpec": {"totalMilesDriven": "DOUBLE", "deliveriesCompleted": "INTEGER", "onTimeDeliveryRate": "DOUBLE", "customerRating": "DOUBLE", "incidentCount": "INTEGER", "trainingCompletionRate": "DOUBLE", "overallPerformanceScore": "DOUBLE"}}, "assignedVehicleIds": {"dataType": {"type": "LIST", "nestedType": {"type": "STRING"}}, "isRequiredInEntity": false, "isStoredExternally": false, "isTimeSeries": false}, "currentRouteId": {"dataType": {"type": "STRING"}, "isRequiredInEntity": false, "isStoredExternally": false, "isTimeSeries": false}, "trainingRecords": {"dataType": {"type": "LIST", "nestedType": {"type": "MAP"}}, "isRequiredInEntity": false, "isStoredExternally": true, "isTimeSeries": false}, "behaviorAnalysis": {"dataType": {"type": "MAP", "nestedType": {"type": "DOUBLE"}}, "isRequiredInEntity": false, "isStoredExternally": true, "isTimeSeries": true, "dataTypeSpec": {"riskScore": "DOUBLE", "improvementAreas": "LIST", "strengths": "LIST", "trainingRecommendations": "LIST"}}}