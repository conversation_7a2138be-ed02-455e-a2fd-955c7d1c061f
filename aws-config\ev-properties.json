{"batteryCapacity": {"dataType": {"type": "DOUBLE"}, "isRequiredInEntity": true, "isStoredExternally": false, "isTimeSeries": false}, "maxChargingPower": {"dataType": {"type": "DOUBLE"}, "isRequiredInEntity": true, "isStoredExternally": false, "isTimeSeries": false}, "chargingConnectorType": {"dataType": {"type": "STRING", "allowedValues": ["ccs", "chademo", "type2", "tesla"]}, "isRequiredInEntity": true, "isStoredExternally": false, "isTimeSeries": false}, "batteryStatus": {"dataType": {"type": "MAP", "nestedType": {"type": "DOUBLE"}}, "isRequiredInEntity": false, "isStoredExternally": true, "isTimeSeries": true, "dataTypeSpec": {"stateOfCharge": "DOUBLE", "stateOfHealth": "DOUBLE", "batteryTemperature": "DOUBLE", "voltage": "DOUBLE", "current": "DOUBLE", "power": "DOUBLE", "estimatedRange": "DOUBLE", "energyConsumptionRate": "DOUBLE", "regenerativeBrakingEnergy": "DOUBLE"}}, "chargingStatus": {"dataType": {"type": "MAP", "nestedType": {"type": "DOUBLE"}}, "isRequiredInEntity": false, "isStoredExternally": true, "isTimeSeries": true, "dataTypeSpec": {"isCharging": "BOOLEAN", "chargingPower": "DOUBLE", "chargingVoltage": "DOUBLE", "chargingCurrent": "DOUBLE", "timeToFullCharge": "INTEGER", "chargingEfficiency": "DOUBLE", "chargingCost": "DOUBLE", "connectedStationId": "STRING"}}, "thermalManagement": {"dataType": {"type": "MAP", "nestedType": {"type": "DOUBLE"}}, "isRequiredInEntity": false, "isStoredExternally": true, "isTimeSeries": true, "dataTypeSpec": {"coolantTemperature": "DOUBLE", "heaterStatus": "BOOLEAN", "coolingFanSpeed": "DOUBLE", "thermalEfficiency": "DOUBLE"}}, "chargingHistory": {"dataType": {"type": "LIST", "nestedType": {"type": "MAP"}}, "isRequiredInEntity": false, "isStoredExternally": true, "isTimeSeries": true}, "preferredChargingStationId": {"dataType": {"type": "STRING"}, "isRequiredInEntity": false, "isStoredExternally": false, "isTimeSeries": false}, "chargingSchedule": {"dataType": {"type": "MAP", "nestedType": {"type": "STRING"}}, "isRequiredInEntity": false, "isStoredExternally": false, "isTimeSeries": false, "dataTypeSpec": {"scheduledStartTime": "STRING", "scheduledEndTime": "STRING", "targetSoC": "DOUBLE", "chargingStrategy": "STRING"}}, "batteryHealthPrediction": {"dataType": {"type": "MAP", "nestedType": {"type": "DOUBLE"}}, "isRequiredInEntity": false, "isStoredExternally": true, "isTimeSeries": true, "dataTypeSpec": {"predictedDegradation": "DOUBLE", "remainingCycles": "INTEGER", "healthScore": "DOUBLE", "replacementRecommendation": "STRING"}}, "energyOptimization": {"dataType": {"type": "MAP", "nestedType": {"type": "DOUBLE"}}, "isRequiredInEntity": false, "isStoredExternally": true, "isTimeSeries": true, "dataTypeSpec": {"optimalChargingPower": "DOUBLE", "costOptimizedSchedule": "STRING", "gridOptimalTiming": "STRING", "renewableEnergyUsage": "DOUBLE"}}}