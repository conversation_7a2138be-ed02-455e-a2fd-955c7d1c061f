AWSTemplateFormatVersion: '2010-09-09'
Description: 'Fleet Management Digital Twins Infrastructure on AWS'

Parameters:
  WorkspaceName:
    Type: String
    Default: fleet-management-workspace
    Description: Name for the IoT TwinMaker workspace
  
  S3BucketName:
    Type: String
    Default: fleet-management-twinmaker-data
    Description: S3 bucket for TwinMaker workspace data
  
  TimestreamDatabaseName:
    Type: String
    Default: FleetManagementDB
    Description: Timestream database name for telemetry data

Resources:
  # S3 Bucket for TwinMaker workspace
  TwinMakerS3Bucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub "${S3BucketName}-${AWS::AccountId}"
      VersioningConfiguration:
        Status: Enabled
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: AES256
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true

  # S3 Bucket for data lake
  DataLakeS3Bucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub "fleet-data-lake-${AWS::AccountId}"
      VersioningConfiguration:
        Status: Enabled
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: AES256
      LifecycleConfiguration:
        Rules:
          - Id: TransitionToIA
            Status: Enabled
            Transitions:
              - TransitionInDays: 30
                StorageClass: STANDARD_IA
          - Id: TransitionToGlacier
            Status: Enabled
            Transitions:
              - TransitionInDays: 90
                StorageClass: GLACIER

  # IAM Role for TwinMaker
  TwinMakerExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: TwinMakerExecutionRole
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: iottwinmaker.amazonaws.com
            Action: sts:AssumeRole
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/AWSIoTTwinMakerServiceRolePolicy
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
      Policies:
        - PolicyName: TwinMakerS3Access
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - s3:GetObject
                  - s3:PutObject
                  - s3:DeleteObject
                  - s3:ListBucket
                Resource:
                  - !Sub "${TwinMakerS3Bucket}/*"
                  - !GetAtt TwinMakerS3Bucket.Arn
        - PolicyName: TimestreamAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - timestream:Select
                  - timestream:DescribeTable
                  - timestream:ListTables
                  - timestream:DescribeDatabase
                  - timestream:ListDatabases
                Resource: "*"

  # Timestream Database
  TimestreamDatabase:
    Type: AWS::Timestream::Database
    Properties:
      DatabaseName: !Ref TimestreamDatabaseName

  # Timestream Tables
  VehicleTelemetryTable:
    Type: AWS::Timestream::Table
    Properties:
      DatabaseName: !Ref TimestreamDatabase
      TableName: VehicleTelemetry
      RetentionProperties:
        MemoryStoreRetentionPeriodInHours: 24
        MagneticStoreRetentionPeriodInDays: 365

  DriverBehaviorTable:
    Type: AWS::Timestream::Table
    Properties:
      DatabaseName: !Ref TimestreamDatabase
      TableName: DriverBehavior
      RetentionProperties:
        MemoryStoreRetentionPeriodInHours: 24
        MagneticStoreRetentionPeriodInDays: 365

  ChargingDataTable:
    Type: AWS::Timestream::Table
    Properties:
      DatabaseName: !Ref TimestreamDatabase
      TableName: ChargingData
      RetentionProperties:
        MemoryStoreRetentionPeriodInHours: 24
        MagneticStoreRetentionPeriodInDays: 365

  # Kinesis Data Stream for real-time telemetry
  VehicleTelemetryStream:
    Type: AWS::Kinesis::Stream
    Properties:
      Name: fleet-vehicle-telemetry
      ShardCount: 5
      StreamEncryption:
        EncryptionType: KMS
        KeyId: alias/aws/kinesis

  # Kinesis Data Firehose for data lake
  TelemetryFirehose:
    Type: AWS::KinesisFirehose::DeliveryStream
    Properties:
      DeliveryStreamName: fleet-telemetry-firehose
      DeliveryStreamType: KinesisStreamAsSource
      KinesisStreamSourceConfiguration:
        KinesisStreamARN: !GetAtt VehicleTelemetryStream.Arn
        RoleARN: !GetAtt FirehoseDeliveryRole.Arn
      S3DestinationConfiguration:
        BucketARN: !GetAtt DataLakeS3Bucket.Arn
        Prefix: "telemetry/year=!{timestamp:yyyy}/month=!{timestamp:MM}/day=!{timestamp:dd}/hour=!{timestamp:HH}/"
        ErrorOutputPrefix: "errors/"
        BufferingHints:
          SizeInMBs: 5
          IntervalInSeconds: 300
        CompressionFormat: GZIP
        RoleARN: !GetAtt FirehoseDeliveryRole.Arn
        ProcessingConfiguration:
          Enabled: true
          Processors:
            - Type: Lambda
              Parameters:
                - ParameterName: LambdaArn
                  ParameterValue: !GetAtt TelemetryProcessorFunction.Arn

  # IAM Role for Firehose
  FirehoseDeliveryRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: firehose.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: FirehoseDeliveryPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - s3:AbortMultipartUpload
                  - s3:GetBucketLocation
                  - s3:GetObject
                  - s3:ListBucket
                  - s3:ListBucketMultipartUploads
                  - s3:PutObject
                Resource:
                  - !GetAtt DataLakeS3Bucket.Arn
                  - !Sub "${DataLakeS3Bucket}/*"
              - Effect: Allow
                Action:
                  - kinesis:DescribeStream
                  - kinesis:GetShardIterator
                  - kinesis:GetRecords
                Resource: !GetAtt VehicleTelemetryStream.Arn
              - Effect: Allow
                Action:
                  - lambda:InvokeFunction
                  - lambda:GetFunctionConfiguration
                Resource: !GetAtt TelemetryProcessorFunction.Arn

  # Lambda function for telemetry processing
  TelemetryProcessorFunction:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: fleet-telemetry-processor
      Runtime: python3.9
      Handler: index.lambda_handler
      Role: !GetAtt TelemetryProcessorRole.Arn
      Timeout: 300
      MemorySize: 512
      Environment:
        Variables:
          TIMESTREAM_DATABASE: !Ref TimestreamDatabase
          VEHICLE_TABLE: !Ref VehicleTelemetryTable
          DRIVER_TABLE: !Ref DriverBehaviorTable
          CHARGING_TABLE: !Ref ChargingDataTable
          TWINMAKER_WORKSPACE: !Ref WorkspaceName
      Code:
        ZipFile: |
          import json
          import boto3
          import base64
          import os
          from datetime import datetime
          
          timestream = boto3.client('timestream-write')
          twinmaker = boto3.client('iottwinmaker')
          
          def lambda_handler(event, context):
              output = []
              
              for record in event['records']:
                  # Decode the data
                  payload = base64.b64decode(record['data'])
                  data = json.loads(payload)
                  
                  # Process telemetry data
                  process_telemetry(data)
                  
                  # Return processed record
                  output_record = {
                      'recordId': record['recordId'],
                      'result': 'Ok',
                      'data': record['data']
                  }
                  output.append(output_record)
              
              return {'records': output}
          
          def process_telemetry(data):
              # Write to Timestream
              if 'vehicleId' in data:
                  write_vehicle_telemetry(data)
              elif 'driverId' in data:
                  write_driver_behavior(data)
              elif 'stationId' in data:
                  write_charging_data(data)
          
          def write_vehicle_telemetry(data):
              records = []
              current_time = str(int(datetime.now().timestamp() * 1000))
              
              for key, value in data.items():
                  if key != 'vehicleId' and isinstance(value, (int, float)):
                      records.append({
                          'Dimensions': [
                              {'Name': 'vehicleId', 'Value': data['vehicleId']}
                          ],
                          'MeasureName': key,
                          'MeasureValue': str(value),
                          'MeasureValueType': 'DOUBLE',
                          'Time': current_time
                      })
              
              if records:
                  timestream.write_records(
                      DatabaseName=os.environ['TIMESTREAM_DATABASE'],
                      TableName=os.environ['VEHICLE_TABLE'],
                      Records=records
                  )

  # IAM Role for Lambda
  TelemetryProcessorRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
      Policies:
        - PolicyName: TimestreamWriteAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - timestream:WriteRecords
                  - timestream:DescribeEndpoints
                Resource: "*"
        - PolicyName: TwinMakerAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - iottwinmaker:UpdateEntity
                  - iottwinmaker:GetEntity
                  - iottwinmaker:UpdatePropertyValues
                Resource: "*"

  # IoT Rule for routing telemetry data
  VehicleTelemetryRule:
    Type: AWS::IoT::TopicRule
    Properties:
      RuleName: FleetVehicleTelemetryRule
      TopicRulePayload:
        RuleDisabled: false
        Sql: "SELECT * FROM 'fleet/vehicles/+/telemetry'"
        Actions:
          - Kinesis:
              StreamName: !Ref VehicleTelemetryStream
              PartitionKey: "${vehicleId}"
              RoleArn: !GetAtt IoTRuleRole.Arn

  # IoT Rule Role
  IoTRuleRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: iot.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: KinesisAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - kinesis:PutRecord
                  - kinesis:PutRecords
                Resource: !GetAtt VehicleTelemetryStream.Arn

Outputs:
  TwinMakerWorkspaceId:
    Description: IoT TwinMaker Workspace ID
    Value: !Ref WorkspaceName
    Export:
      Name: !Sub "${AWS::StackName}-TwinMakerWorkspace"
  
  TimestreamDatabase:
    Description: Timestream Database Name
    Value: !Ref TimestreamDatabase
    Export:
      Name: !Sub "${AWS::StackName}-TimestreamDB"
  
  S3BucketName:
    Description: S3 Bucket for TwinMaker
    Value: !Ref TwinMakerS3Bucket
    Export:
      Name: !Sub "${AWS::StackName}-S3Bucket"
  
  KinesisStreamName:
    Description: Kinesis Stream for telemetry
    Value: !Ref VehicleTelemetryStream
    Export:
      Name: !Sub "${AWS::StackName}-KinesisStream"
