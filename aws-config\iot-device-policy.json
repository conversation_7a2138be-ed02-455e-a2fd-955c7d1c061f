{"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["iot:Connect"], "Resource": ["arn:aws:iot:*:*:client/${iot:Connection.Thing.ThingName}"]}, {"Effect": "Allow", "Action": ["iot:Publish"], "Resource": ["arn:aws:iot:*:*:topic/fleet/vehicles/${iot:Connection.Thing.ThingName}/telemetry", "arn:aws:iot:*:*:topic/fleet/vehicles/${iot:Connection.Thing.ThingName}/diagnostics", "arn:aws:iot:*:*:topic/fleet/vehicles/${iot:Connection.Thing.ThingName}/location", "arn:aws:iot:*:*:topic/fleet/vehicles/${iot:Connection.Thing.ThingName}/battery", "arn:aws:iot:*:*:topic/fleet/vehicles/${iot:Connection.Thing.ThingName}/charging", "arn:aws:iot:*:*:topic/fleet/drivers/${iot:Connection.Thing.ThingName}/behavior", "arn:aws:iot:*:*:topic/fleet/charging-stations/${iot:Connection.Thing.ThingName}/status", "arn:aws:iot:*:*:topic/fleet/charging-stations/${iot:Connection.Thing.ThingName}/usage"]}, {"Effect": "Allow", "Action": ["iot:Subscribe"], "Resource": ["arn:aws:iot:*:*:topicfilter/fleet/vehicles/${iot:Connection.Thing.ThingName}/commands/*", "arn:aws:iot:*:*:topicfilter/fleet/drivers/${iot:Connection.Thing.ThingName}/commands/*", "arn:aws:iot:*:*:topicfilter/fleet/charging-stations/${iot:Connection.Thing.ThingName}/commands/*", "arn:aws:iot:*:*:topicfilter/fleet/optimization/*", "arn:aws:iot:*:*:topicfilter/fleet/maintenance/*", "arn:aws:iot:*:*:topicfilter/fleet/alerts/*"]}, {"Effect": "Allow", "Action": ["iot:Receive"], "Resource": ["arn:aws:iot:*:*:topic/fleet/vehicles/${iot:Connection.Thing.ThingName}/commands/*", "arn:aws:iot:*:*:topic/fleet/drivers/${iot:Connection.Thing.ThingName}/commands/*", "arn:aws:iot:*:*:topic/fleet/charging-stations/${iot:Connection.Thing.ThingName}/commands/*", "arn:aws:iot:*:*:topic/fleet/optimization/*", "arn:aws:iot:*:*:topic/fleet/maintenance/*", "arn:aws:iot:*:*:topic/fleet/alerts/*"]}, {"Effect": "Allow", "Action": ["iot:UpdateThingShadow", "iot:GetThingShadow"], "Resource": ["arn:aws:iot:*:*:thing/${iot:Connection.Thing.ThingName}"]}]}