import json
import boto3
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import os
import logging
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler
import pickle
import base64

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Initialize AWS clients
timestream_query = boto3.client('timestream-query')
twinmaker = boto3.client('iottwinmaker')
sns = boto3.client('sns')
s3 = boto3.client('s3')

# Environment variables
TIMESTREAM_DATABASE = os.environ.get('TIMESTREAM_DATABASE', 'FleetManagementDB')
TIMESTREAM_TABLE = os.environ.get('TIMESTREAM_TABLE', 'VehicleTelemetry')
TWINMAKER_WORKSPACE = os.environ.get('TWINMAKER_WORKSPACE', 'fleet-management-workspace')
SNS_TOPIC_ARN = os.environ.get('SNS_TOPIC_ARN')
MODEL_S3_BUCKET = os.environ.get('MODEL_S3_BUCKET')
MODEL_S3_KEY = os.environ.get('MODEL_S3_KEY', 'models/anomaly-detection-model.pkl')

# Global variables for model caching
anomaly_model = None
scaler = None

def lambda_handler(event, context):
    """
    Main Lambda handler for anomaly detection
    """
    try:
        # Load model if not already cached
        load_model()
        
        # Parse the incoming event
        if 'Records' in event:
            # Process Kinesis records
            for record in event['Records']:
                if record['eventSource'] == 'aws:kinesis':
                    process_kinesis_record(record)
        else:
            # Direct invocation with vehicle ID
            vehicle_id = event.get('vehicleId')
            if vehicle_id:
                result = detect_anomalies(vehicle_id)
                return {
                    'statusCode': 200,
                    'body': json.dumps(result)
                }
        
        return {
            'statusCode': 200,
            'body': json.dumps({'message': 'Anomaly detection completed successfully'})
        }
        
    except Exception as e:
        logger.error(f"Error in anomaly detection: {str(e)}")
        return {
            'statusCode': 500,
            'body': json.dumps({'error': str(e)})
        }

def load_model():
    """
    Load the anomaly detection model from S3
    """
    global anomaly_model, scaler
    
    try:
        if anomaly_model is None and MODEL_S3_BUCKET:
            # Download model from S3
            response = s3.get_object(Bucket=MODEL_S3_BUCKET, Key=MODEL_S3_KEY)
            model_data = pickle.loads(response['Body'].read())
            
            anomaly_model = model_data.get('model')
            scaler = model_data.get('scaler')
            
            logger.info("Anomaly detection model loaded successfully")
        elif anomaly_model is None:
            # Create a default model if no S3 model is available
            anomaly_model = IsolationForest(
                contamination=0.1,
                random_state=42,
                n_estimators=100
            )
            scaler = StandardScaler()
            logger.info("Using default anomaly detection model")
            
    except Exception as e:
        logger.error(f"Error loading model: {str(e)}")
        # Fallback to default model
        anomaly_model = IsolationForest(contamination=0.1, random_state=42)
        scaler = StandardScaler()

def process_kinesis_record(record):
    """
    Process individual Kinesis record for real-time anomaly detection
    """
    try:
        # Decode the Kinesis data
        payload = base64.b64decode(record['kinesis']['data'])
        data = json.loads(payload)
        
        vehicle_id = data.get('vehicleId')
        if vehicle_id:
            # Perform real-time anomaly detection
            result = detect_real_time_anomaly(vehicle_id, data)
            
            # Update digital twin with results
            if result:
                update_digital_twin(vehicle_id, result)
                
                # Send alerts if anomaly detected
                if result.get('anomaly_detected', False):
                    send_anomaly_alert(vehicle_id, result)
                
    except Exception as e:
        logger.error(f"Error processing Kinesis record: {str(e)}")

def detect_real_time_anomaly(vehicle_id, current_data):
    """
    Detect anomalies in real-time telemetry data
    """
    try:
        # Extract relevant metrics from current data
        metrics = extract_metrics(current_data)
        
        if not metrics:
            return None
        
        # Get historical baseline for comparison
        baseline_data = get_baseline_data(vehicle_id)
        
        # Perform anomaly detection
        anomaly_result = analyze_anomalies(vehicle_id, metrics, baseline_data)
        
        return anomaly_result
        
    except Exception as e:
        logger.error(f"Error in real-time anomaly detection: {str(e)}")
        return None

def detect_anomalies(vehicle_id, hours_back=1):
    """
    Perform comprehensive anomaly detection for a specific vehicle
    """
    try:
        # Get recent telemetry data
        telemetry_data = get_vehicle_telemetry(vehicle_id, hours_back)
        
        if telemetry_data is None or len(telemetry_data) == 0:
            logger.warning(f"No telemetry data found for vehicle {vehicle_id}")
            return {'error': 'No telemetry data available'}
        
        # Prepare data for anomaly detection
        processed_data = prepare_anomaly_features(telemetry_data)
        
        if processed_data is None or len(processed_data) == 0:
            return {'error': 'Insufficient data for anomaly detection'}
        
        # Perform anomaly detection
        anomalies = detect_anomalies_in_data(processed_data)
        
        # Analyze and categorize anomalies
        result = analyze_anomaly_results(vehicle_id, anomalies, processed_data)
        
        logger.info(f"Anomaly detection completed for vehicle {vehicle_id}")
        return result
        
    except Exception as e:
        logger.error(f"Error in anomaly detection for vehicle {vehicle_id}: {str(e)}")
        raise

def get_vehicle_telemetry(vehicle_id, hours_back=1):
    """
    Retrieve vehicle telemetry data from Timestream
    """
    try:
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours_back)
        
        query = f"""
        SELECT 
            time,
            measure_name,
            measure_value::double as value
        FROM "{TIMESTREAM_DATABASE}"."{TIMESTREAM_TABLE}"
        WHERE vehicleId = '{vehicle_id}'
        AND time BETWEEN '{start_time.isoformat()}' AND '{end_time.isoformat()}'
        AND measure_name IN (
            'engineTemperature', 'oilPressure', 'rpm', 'fuelConsumptionRate',
            'batteryVoltage', 'speed', 'brakeWearLevel', 'tirePressureFrontLeft',
            'tirePressureFrontRight', 'tirePressureRearLeft', 'tirePressureRearRight'
        )
        ORDER BY time ASC
        """
        
        response = timestream_query.query(QueryString=query)
        
        # Convert to pandas DataFrame
        data = []
        for row in response['Rows']:
            data.append({
                'time': row['Data'][0]['ScalarValue'],
                'measure_name': row['Data'][1]['ScalarValue'],
                'value': float(row['Data'][2]['ScalarValue'])
            })
        
        df = pd.DataFrame(data)
        return df
        
    except Exception as e:
        logger.error(f"Error retrieving telemetry data: {str(e)}")
        return None

def get_baseline_data(vehicle_id, days_back=7):
    """
    Get baseline data for comparison
    """
    try:
        end_time = datetime.now() - timedelta(hours=1)  # Exclude recent data
        start_time = end_time - timedelta(days=days_back)
        
        query = f"""
        SELECT 
            measure_name,
            AVG(measure_value::double) as avg_value,
            STDDEV(measure_value::double) as std_value,
            MIN(measure_value::double) as min_value,
            MAX(measure_value::double) as max_value
        FROM "{TIMESTREAM_DATABASE}"."{TIMESTREAM_TABLE}"
        WHERE vehicleId = '{vehicle_id}'
        AND time BETWEEN '{start_time.isoformat()}' AND '{end_time.isoformat()}'
        GROUP BY measure_name
        """
        
        response = timestream_query.query(QueryString=query)
        
        baseline = {}
        for row in response['Rows']:
            measure_name = row['Data'][0]['ScalarValue']
            baseline[measure_name] = {
                'avg': float(row['Data'][1]['ScalarValue']),
                'std': float(row['Data'][2]['ScalarValue']),
                'min': float(row['Data'][3]['ScalarValue']),
                'max': float(row['Data'][4]['ScalarValue'])
            }
        
        return baseline
        
    except Exception as e:
        logger.error(f"Error retrieving baseline data: {str(e)}")
        return {}

def extract_metrics(data):
    """
    Extract relevant metrics from telemetry data
    """
    try:
        metrics = {}
        
        # Engine metrics
        if 'engineTemperature' in data:
            metrics['engineTemperature'] = float(data['engineTemperature'])
        if 'oilPressure' in data:
            metrics['oilPressure'] = float(data['oilPressure'])
        if 'rpm' in data:
            metrics['rpm'] = float(data['rpm'])
        
        # Vehicle metrics
        if 'speed' in data:
            metrics['speed'] = float(data['speed'])
        if 'fuelConsumptionRate' in data:
            metrics['fuelConsumptionRate'] = float(data['fuelConsumptionRate'])
        if 'batteryVoltage' in data:
            metrics['batteryVoltage'] = float(data['batteryVoltage'])
        
        # Tire pressure metrics
        tire_pressures = []
        for tire in ['tirePressureFrontLeft', 'tirePressureFrontRight', 
                    'tirePressureRearLeft', 'tirePressureRearRight']:
            if tire in data:
                tire_pressures.append(float(data[tire]))
        
        if tire_pressures:
            metrics['avgTirePressure'] = np.mean(tire_pressures)
            metrics['tirePressureVariance'] = np.var(tire_pressures)
        
        return metrics
        
    except Exception as e:
        logger.error(f"Error extracting metrics: {str(e)}")
        return {}

def prepare_anomaly_features(telemetry_data):
    """
    Prepare features for anomaly detection
    """
    try:
        # Pivot the data to get features as columns
        df_pivot = telemetry_data.pivot_table(
            index='time', 
            columns='measure_name', 
            values='value', 
            aggfunc='mean'
        )
        
        # Fill missing values with forward fill then backward fill
        df_pivot = df_pivot.fillna(method='ffill').fillna(method='bfill')
        
        # Calculate additional features
        if 'tirePressureFrontLeft' in df_pivot.columns:
            tire_cols = [col for col in df_pivot.columns if 'tirePressure' in col]
            if len(tire_cols) > 1:
                df_pivot['avgTirePressure'] = df_pivot[tire_cols].mean(axis=1)
                df_pivot['tirePressureVariance'] = df_pivot[tire_cols].var(axis=1)
        
        # Remove any remaining NaN values
        df_pivot = df_pivot.dropna()
        
        return df_pivot
        
    except Exception as e:
        logger.error(f"Error preparing anomaly features: {str(e)}")
        return None

def detect_anomalies_in_data(data):
    """
    Detect anomalies using the loaded model
    """
    try:
        global anomaly_model, scaler
        
        # Prepare features for the model
        feature_columns = [
            'engineTemperature', 'oilPressure', 'rpm', 'fuelConsumptionRate',
            'batteryVoltage', 'speed', 'brakeWearLevel'
        ]
        
        # Select available features
        available_features = [col for col in feature_columns if col in data.columns]
        
        if len(available_features) < 3:
            logger.warning("Insufficient features for anomaly detection")
            return []
        
        feature_data = data[available_features].values
        
        # Scale the features
        if scaler is not None:
            try:
                feature_data = scaler.transform(feature_data)
            except:
                # If scaler fails, fit a new one
                scaler.fit(feature_data)
                feature_data = scaler.transform(feature_data)
        else:
            scaler = StandardScaler()
            feature_data = scaler.fit_transform(feature_data)
        
        # Detect anomalies
        anomaly_scores = anomaly_model.decision_function(feature_data)
        anomaly_labels = anomaly_model.predict(feature_data)
        
        # Combine results with timestamps
        anomalies = []
        for i, (score, label) in enumerate(zip(anomaly_scores, anomaly_labels)):
            if label == -1:  # Anomaly detected
                anomalies.append({
                    'timestamp': data.index[i],
                    'anomaly_score': float(score),
                    'features': {col: float(data.iloc[i][col]) for col in available_features}
                })
        
        return anomalies
        
    except Exception as e:
        logger.error(f"Error detecting anomalies: {str(e)}")
        return []

def analyze_anomalies(vehicle_id, current_metrics, baseline_data):
    """
    Analyze current metrics against baseline for anomalies
    """
    try:
        anomalies = []
        anomaly_score = 0.0
        
        for metric, value in current_metrics.items():
            if metric in baseline_data:
                baseline = baseline_data[metric]
                
                # Calculate z-score
                if baseline['std'] > 0:
                    z_score = abs(value - baseline['avg']) / baseline['std']
                    
                    # Check for anomaly (z-score > 3 is typically considered anomalous)
                    if z_score > 3:
                        anomaly_type = determine_anomaly_type(metric, value, baseline)
                        severity = determine_severity(z_score)
                        
                        anomalies.append({
                            'metric': metric,
                            'current_value': value,
                            'expected_range': {
                                'min': baseline['min'],
                                'max': baseline['max'],
                                'avg': baseline['avg']
                            },
                            'z_score': z_score,
                            'anomaly_type': anomaly_type,
                            'severity': severity
                        })
                        
                        # Contribute to overall anomaly score
                        anomaly_score += min(z_score / 10, 1.0)  # Normalize to 0-1
        
        # Cap anomaly score at 1.0
        anomaly_score = min(anomaly_score, 1.0)
        
        result = {
            'vehicle_id': vehicle_id,
            'timestamp': datetime.now().isoformat(),
            'anomaly_detected': len(anomalies) > 0,
            'anomaly_score': anomaly_score * 100,  # Convert to 0-100 scale
            'anomalies': anomalies,
            'total_anomalies': len(anomalies)
        }
        
        return result
        
    except Exception as e:
        logger.error(f"Error analyzing anomalies: {str(e)}")
        return None

def analyze_anomaly_results(vehicle_id, anomalies, data):
    """
    Analyze and categorize anomaly detection results
    """
    try:
        if not anomalies:
            return {
                'vehicle_id': vehicle_id,
                'timestamp': datetime.now().isoformat(),
                'anomaly_detected': False,
                'anomaly_score': 0.0,
                'anomalies': [],
                'summary': 'No anomalies detected'
            }
        
        # Calculate overall anomaly score
        scores = [abs(anomaly['anomaly_score']) for anomaly in anomalies]
        overall_score = np.mean(scores) if scores else 0.0
        
        # Categorize anomalies by severity
        severity_counts = {'low': 0, 'medium': 0, 'high': 0, 'critical': 0}
        
        for anomaly in anomalies:
            score = abs(anomaly['anomaly_score'])
            if score > 0.8:
                severity = 'critical'
            elif score > 0.6:
                severity = 'high'
            elif score > 0.4:
                severity = 'medium'
            else:
                severity = 'low'
            
            severity_counts[severity] += 1
            anomaly['severity'] = severity
        
        # Determine overall severity
        if severity_counts['critical'] > 0:
            overall_severity = 'critical'
        elif severity_counts['high'] > 0:
            overall_severity = 'high'
        elif severity_counts['medium'] > 0:
            overall_severity = 'medium'
        else:
            overall_severity = 'low'
        
        result = {
            'vehicle_id': vehicle_id,
            'timestamp': datetime.now().isoformat(),
            'anomaly_detected': True,
            'anomaly_score': abs(overall_score) * 100,  # Convert to 0-100 scale
            'overall_severity': overall_severity,
            'anomalies': anomalies,
            'total_anomalies': len(anomalies),
            'severity_breakdown': severity_counts,
            'analysis_period': f"{len(data)} data points analyzed"
        }
        
        return result
        
    except Exception as e:
        logger.error(f"Error analyzing anomaly results: {str(e)}")
        return None

def determine_anomaly_type(metric, value, baseline):
    """
    Determine the type of anomaly based on the metric and value
    """
    if 'temperature' in metric.lower():
        return 'thermal_anomaly' if value > baseline['avg'] else 'cooling_anomaly'
    elif 'pressure' in metric.lower():
        return 'pressure_high' if value > baseline['avg'] else 'pressure_low'
    elif 'rpm' in metric.lower():
        return 'engine_overspeed' if value > baseline['avg'] else 'engine_underspeed'
    elif 'consumption' in metric.lower():
        return 'high_consumption' if value > baseline['avg'] else 'low_consumption'
    elif 'voltage' in metric.lower():
        return 'electrical_high' if value > baseline['avg'] else 'electrical_low'
    else:
        return 'performance_anomaly'

def determine_severity(z_score):
    """
    Determine severity based on z-score
    """
    if z_score > 5:
        return 'critical'
    elif z_score > 4:
        return 'high'
    elif z_score > 3:
        return 'medium'
    else:
        return 'low'

def update_digital_twin(vehicle_id, anomaly_result):
    """
    Update the digital twin with anomaly detection results
    """
    try:
        property_updates = {
            'anomalyScore': {
                'value': {
                    'doubleValue': anomaly_result['anomaly_score']
                }
            }
        }
        
        twinmaker.update_entity(
            workspaceId=TWINMAKER_WORKSPACE,
            entityId=vehicle_id,
            componentUpdates={
                'VehicleComponent': {
                    'propertyUpdates': property_updates
                }
            }
        )
        
        logger.info(f"Updated digital twin for vehicle {vehicle_id} with anomaly score")
        
    except Exception as e:
        logger.error(f"Error updating digital twin: {str(e)}")

def send_anomaly_alert(vehicle_id, anomaly_result):
    """
    Send anomaly alert via SNS
    """
    try:
        if not SNS_TOPIC_ARN:
            logger.warning("SNS topic not configured, skipping alert")
            return
        
        message = {
            'alert_type': 'anomaly_detection',
            'vehicle_id': vehicle_id,
            'anomaly_score': anomaly_result['anomaly_score'],
            'severity': anomaly_result.get('overall_severity', 'unknown'),
            'total_anomalies': anomaly_result['total_anomalies'],
            'timestamp': anomaly_result['timestamp']
        }
        
        sns.publish(
            TopicArn=SNS_TOPIC_ARN,
            Message=json.dumps(message),
            Subject=f"Anomaly Alert - Vehicle {vehicle_id}"
        )
        
        logger.info(f"Sent anomaly alert for vehicle {vehicle_id}")
        
    except Exception as e:
        logger.error(f"Error sending anomaly alert: {str(e)}")
