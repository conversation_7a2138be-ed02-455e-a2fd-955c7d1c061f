import json
import boto3
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import os
import logging

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Initialize AWS clients
sagemaker_runtime = boto3.client('sagemaker-runtime')
timestream_query = boto3.client('timestream-query')
twinmaker = boto3.client('iottwinmaker')
sns = boto3.client('sns')

# Environment variables
SAGEMAKER_ENDPOINT = os.environ.get('SAGEMAKER_ENDPOINT', 'predictive-maintenance-endpoint')
TIMESTREAM_DATABASE = os.environ.get('TIMESTREAM_DATABASE', 'FleetManagementDB')
TIMESTREAM_TABLE = os.environ.get('TIMESTREAM_TABLE', 'VehicleTelemetry')
TWINMAKER_WORKSPACE = os.environ.get('TWINMAKER_WORKSPACE', 'fleet-management-workspace')
SNS_TOPIC_ARN = os.environ.get('SNS_TOPIC_ARN')

def lambda_handler(event, context):
    """
    Main Lambda handler for predictive maintenance analysis
    """
    try:
        # Parse the incoming event
        if 'Records' in event:
            # Process Kinesis records
            for record in event['Records']:
                if record['eventSource'] == 'aws:kinesis':
                    process_kinesis_record(record)
        else:
            # Direct invocation with vehicle ID
            vehicle_id = event.get('vehicleId')
            if vehicle_id:
                result = analyze_vehicle_maintenance(vehicle_id)
                return {
                    'statusCode': 200,
                    'body': json.dumps(result)
                }
        
        return {
            'statusCode': 200,
            'body': json.dumps({'message': 'Processing completed successfully'})
        }
        
    except Exception as e:
        logger.error(f"Error in predictive maintenance analysis: {str(e)}")
        return {
            'statusCode': 500,
            'body': json.dumps({'error': str(e)})
        }

def process_kinesis_record(record):
    """
    Process individual Kinesis record for real-time analysis
    """
    try:
        # Decode the Kinesis data
        import base64
        payload = base64.b64decode(record['kinesis']['data'])
        data = json.loads(payload)
        
        vehicle_id = data.get('vehicleId')
        if vehicle_id:
            # Perform predictive maintenance analysis
            result = analyze_vehicle_maintenance(vehicle_id)
            
            # Update digital twin with results
            update_digital_twin(vehicle_id, result)
            
            # Send alerts if necessary
            if result.get('failure_probability', 0) > 0.7:
                send_maintenance_alert(vehicle_id, result)
                
    except Exception as e:
        logger.error(f"Error processing Kinesis record: {str(e)}")

def analyze_vehicle_maintenance(vehicle_id):
    """
    Perform predictive maintenance analysis for a specific vehicle
    """
    try:
        # Get historical telemetry data
        telemetry_data = get_vehicle_telemetry(vehicle_id)
        
        if not telemetry_data:
            logger.warning(f"No telemetry data found for vehicle {vehicle_id}")
            return {'error': 'No telemetry data available'}
        
        # Prepare features for ML model
        features = prepare_features(telemetry_data)
        
        # Make prediction using SageMaker endpoint
        prediction = invoke_sagemaker_model(features)
        
        # Process prediction results
        result = process_prediction_results(vehicle_id, prediction, telemetry_data)
        
        logger.info(f"Predictive maintenance analysis completed for vehicle {vehicle_id}")
        return result
        
    except Exception as e:
        logger.error(f"Error in maintenance analysis for vehicle {vehicle_id}: {str(e)}")
        raise

def get_vehicle_telemetry(vehicle_id, hours_back=24):
    """
    Retrieve vehicle telemetry data from Timestream
    """
    try:
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours_back)
        
        query = f"""
        SELECT 
            time,
            measure_name,
            measure_value::double as value
        FROM "{TIMESTREAM_DATABASE}"."{TIMESTREAM_TABLE}"
        WHERE vehicleId = '{vehicle_id}'
        AND time BETWEEN '{start_time.isoformat()}' AND '{end_time.isoformat()}'
        ORDER BY time DESC
        """
        
        response = timestream_query.query(QueryString=query)
        
        # Convert to pandas DataFrame for easier processing
        data = []
        for row in response['Rows']:
            data.append({
                'time': row['Data'][0]['ScalarValue'],
                'measure_name': row['Data'][1]['ScalarValue'],
                'value': float(row['Data'][2]['ScalarValue'])
            })
        
        df = pd.DataFrame(data)
        return df
        
    except Exception as e:
        logger.error(f"Error retrieving telemetry data: {str(e)}")
        return None

def prepare_features(telemetry_data):
    """
    Prepare features for the ML model from telemetry data
    """
    try:
        # Pivot the data to get features as columns
        df_pivot = telemetry_data.pivot_table(
            index='time', 
            columns='measure_name', 
            values='value', 
            aggfunc='mean'
        )
        
        # Calculate statistical features
        features = {}
        
        # Engine metrics
        if 'engineTemperature' in df_pivot.columns:
            features['engine_temp_mean'] = df_pivot['engineTemperature'].mean()
            features['engine_temp_std'] = df_pivot['engineTemperature'].std()
            features['engine_temp_max'] = df_pivot['engineTemperature'].max()
        
        if 'oilPressure' in df_pivot.columns:
            features['oil_pressure_mean'] = df_pivot['oilPressure'].mean()
            features['oil_pressure_std'] = df_pivot['oilPressure'].std()
            features['oil_pressure_min'] = df_pivot['oilPressure'].min()
        
        if 'rpm' in df_pivot.columns:
            features['rpm_mean'] = df_pivot['rpm'].mean()
            features['rpm_std'] = df_pivot['rpm'].std()
            features['rpm_max'] = df_pivot['rpm'].max()
        
        # Fuel/Energy metrics
        if 'fuelConsumptionRate' in df_pivot.columns:
            features['fuel_consumption_mean'] = df_pivot['fuelConsumptionRate'].mean()
            features['fuel_consumption_trend'] = calculate_trend(df_pivot['fuelConsumptionRate'])
        
        # Vehicle health metrics
        if 'brakeWearLevel' in df_pivot.columns:
            features['brake_wear'] = df_pivot['brakeWearLevel'].iloc[-1] if len(df_pivot) > 0 else 0
        
        if 'batteryVoltage' in df_pivot.columns:
            features['battery_voltage_mean'] = df_pivot['batteryVoltage'].mean()
            features['battery_voltage_std'] = df_pivot['batteryVoltage'].std()
        
        # Tire pressure features
        tire_columns = [col for col in df_pivot.columns if 'tirePressure' in col]
        if tire_columns:
            features['tire_pressure_variance'] = df_pivot[tire_columns].var().mean()
        
        # Fill missing values with 0
        for key, value in features.items():
            if pd.isna(value):
                features[key] = 0.0
        
        return features
        
    except Exception as e:
        logger.error(f"Error preparing features: {str(e)}")
        return {}

def calculate_trend(series):
    """
    Calculate trend in a time series (positive = increasing, negative = decreasing)
    """
    try:
        if len(series) < 2:
            return 0.0
        
        x = np.arange(len(series))
        y = series.values
        
        # Remove NaN values
        mask = ~np.isnan(y)
        if np.sum(mask) < 2:
            return 0.0
        
        x = x[mask]
        y = y[mask]
        
        # Calculate linear regression slope
        slope = np.polyfit(x, y, 1)[0]
        return float(slope)
        
    except Exception:
        return 0.0

def invoke_sagemaker_model(features):
    """
    Invoke SageMaker endpoint for prediction
    """
    try:
        # Convert features to the format expected by the model
        feature_vector = [
            features.get('engine_temp_mean', 0),
            features.get('engine_temp_std', 0),
            features.get('engine_temp_max', 0),
            features.get('oil_pressure_mean', 0),
            features.get('oil_pressure_std', 0),
            features.get('oil_pressure_min', 0),
            features.get('rpm_mean', 0),
            features.get('rpm_std', 0),
            features.get('rpm_max', 0),
            features.get('fuel_consumption_mean', 0),
            features.get('fuel_consumption_trend', 0),
            features.get('brake_wear', 0),
            features.get('battery_voltage_mean', 0),
            features.get('battery_voltage_std', 0),
            features.get('tire_pressure_variance', 0)
        ]
        
        # Prepare payload for SageMaker
        payload = json.dumps({'instances': [feature_vector]})
        
        # Invoke the endpoint
        response = sagemaker_runtime.invoke_endpoint(
            EndpointName=SAGEMAKER_ENDPOINT,
            ContentType='application/json',
            Body=payload
        )
        
        # Parse the response
        result = json.loads(response['Body'].read().decode())
        return result
        
    except Exception as e:
        logger.error(f"Error invoking SageMaker model: {str(e)}")
        # Return default prediction if model fails
        return {
            'predictions': [{
                'failure_probability': 0.1,
                'days_to_failure': 365,
                'confidence': 0.5
            }]
        }

def process_prediction_results(vehicle_id, prediction, telemetry_data):
    """
    Process and interpret prediction results
    """
    try:
        pred = prediction['predictions'][0]
        
        failure_probability = pred.get('failure_probability', 0)
        days_to_failure = pred.get('days_to_failure', 365)
        confidence = pred.get('confidence', 0.5)
        
        # Determine maintenance recommendation
        if failure_probability > 0.8:
            recommendation = "immediate_maintenance"
            priority = "critical"
        elif failure_probability > 0.6:
            recommendation = "schedule_maintenance"
            priority = "high"
        elif failure_probability > 0.4:
            recommendation = "monitor_closely"
            priority = "medium"
        else:
            recommendation = "normal_operation"
            priority = "low"
        
        # Calculate maintenance score (inverse of failure probability)
        maintenance_score = (1 - failure_probability) * 100
        
        result = {
            'vehicle_id': vehicle_id,
            'timestamp': datetime.now().isoformat(),
            'failure_probability': failure_probability,
            'days_to_failure': int(days_to_failure),
            'confidence': confidence,
            'maintenance_score': maintenance_score,
            'recommendation': recommendation,
            'priority': priority,
            'analysis_details': {
                'data_points_analyzed': len(telemetry_data),
                'model_version': '1.0',
                'features_used': 15
            }
        }
        
        return result
        
    except Exception as e:
        logger.error(f"Error processing prediction results: {str(e)}")
        raise

def update_digital_twin(vehicle_id, analysis_result):
    """
    Update the digital twin with maintenance analysis results
    """
    try:
        # Update vehicle entity in TwinMaker
        property_updates = {
            'maintenanceScore': {
                'value': {
                    'doubleValue': analysis_result['maintenance_score']
                }
            },
            'anomalyScore': {
                'value': {
                    'doubleValue': analysis_result['failure_probability'] * 100
                }
            }
        }
        
        twinmaker.update_entity(
            workspaceId=TWINMAKER_WORKSPACE,
            entityId=vehicle_id,
            componentUpdates={
                'VehicleComponent': {
                    'propertyUpdates': property_updates
                }
            }
        )
        
        logger.info(f"Updated digital twin for vehicle {vehicle_id}")
        
    except Exception as e:
        logger.error(f"Error updating digital twin: {str(e)}")

def send_maintenance_alert(vehicle_id, analysis_result):
    """
    Send maintenance alert via SNS
    """
    try:
        if not SNS_TOPIC_ARN:
            logger.warning("SNS topic not configured, skipping alert")
            return
        
        message = {
            'alert_type': 'predictive_maintenance',
            'vehicle_id': vehicle_id,
            'failure_probability': analysis_result['failure_probability'],
            'days_to_failure': analysis_result['days_to_failure'],
            'recommendation': analysis_result['recommendation'],
            'priority': analysis_result['priority'],
            'timestamp': analysis_result['timestamp']
        }
        
        sns.publish(
            TopicArn=SNS_TOPIC_ARN,
            Message=json.dumps(message),
            Subject=f"Maintenance Alert - Vehicle {vehicle_id}"
        )
        
        logger.info(f"Sent maintenance alert for vehicle {vehicle_id}")
        
    except Exception as e:
        logger.error(f"Error sending maintenance alert: {str(e)}")
