import json
import boto3
import os
import logging
from datetime import datetime, timezone
from typing import Dict, List, Any

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Initialize AWS clients
sitewise_client = boto3.client('iotsitewise')
sns_client = boto3.client('sns')

# Environment variables
SITEWISE_FLEET_MODEL_ID = os.environ.get('SITEWISE_FLEET_MODEL_ID')
SITEWISE_VEHICLE_MODEL_ID = os.environ.get('SITEWISE_VEHICLE_MODEL_ID')
SITEWISE_EV_MODEL_ID = os.environ.get('SITEWISE_EV_MODEL_ID')
SITEWISE_CHARGING_MODEL_ID = os.environ.get('SITEWISE_CHARGING_MODEL_ID')
SNS_TOPIC_ARN = os.environ.get('SNS_TOPIC_ARN')

def lambda_handler(event, context):
    """
    Process IoT telemetry data and send to SiteWise
    """
    try:
        logger.info(f"Received event: {json.dumps(event)}")
        
        # Process each record in the event
        for record in event.get('Records', []):
            if 'Sns' in record:
                # SNS message
                message = json.loads(record['Sns']['Message'])
                process_iot_message(message)
            elif 'eventSource' in record and record['eventSource'] == 'aws:iot':
                # Direct IoT message
                process_iot_message(record)
            else:
                # Direct invocation
                process_iot_message(event)
        
        return {
            'statusCode': 200,
            'body': json.dumps('Successfully processed telemetry data')
        }
        
    except Exception as e:
        logger.error(f"Error processing event: {str(e)}")
        raise

def process_iot_message(message: Dict[str, Any]):
    """
    Process individual IoT message and send to SiteWise
    """
    try:
        # Extract vehicle/asset information
        vehicle_id = message.get('vehicleId')
        station_id = message.get('stationId')
        timestamp = message.get('timestamp', datetime.now(timezone.utc).isoformat())
        
        if vehicle_id:
            process_vehicle_telemetry(vehicle_id, message, timestamp)
        elif station_id:
            process_charging_station_data(station_id, message, timestamp)
        else:
            logger.warning("No vehicle_id or station_id found in message")
            
    except Exception as e:
        logger.error(f"Error processing IoT message: {str(e)}")
        raise

def process_vehicle_telemetry(vehicle_id: str, data: Dict[str, Any], timestamp: str):
    """
    Process vehicle telemetry data and send to SiteWise
    """
    try:
        # Find or create vehicle asset
        asset_id = find_or_create_vehicle_asset(vehicle_id, data)
        
        # Prepare property values for SiteWise
        property_values = []
        
        # Engine diagnostics
        engine_data = data.get('engineDiagnostics', {})
        if engine_data:
            property_values.extend([
                create_property_value('EngineTemperature', engine_data.get('engineTemperature'), timestamp),
                create_property_value('OilPressure', engine_data.get('oilPressure'), timestamp),
                create_property_value('RPM', engine_data.get('rpm'), timestamp),
                create_property_value('FuelLevel', engine_data.get('fuelLevel'), timestamp)
            ])
        
        # Location data
        location_data = data.get('location', {})
        if location_data:
            property_values.extend([
                create_property_value('Speed', location_data.get('speed'), timestamp),
                create_property_value('Latitude', location_data.get('latitude'), timestamp),
                create_property_value('Longitude', location_data.get('longitude'), timestamp)
            ])
        
        # Vehicle health
        health_data = data.get('vehicleHealth', {})
        if health_data:
            property_values.extend([
                create_property_value('Odometer', health_data.get('odometer'), timestamp),
                create_property_value('BatteryVoltage', health_data.get('batteryVoltage'), timestamp),
                create_property_value('MaintenanceScore', health_data.get('maintenanceScore'), timestamp)
            ])
        
        # EV-specific data
        battery_data = data.get('batteryStatus', {})
        if battery_data:
            property_values.extend([
                create_property_value('StateOfCharge', battery_data.get('stateOfCharge'), timestamp),
                create_property_value('StateOfHealth', battery_data.get('stateOfHealth'), timestamp),
                create_property_value('BatteryTemperature', battery_data.get('batteryTemperature'), timestamp),
                create_property_value('EstimatedRange', battery_data.get('estimatedRange'), timestamp)
            ])
        
        # Charging data
        charging_data = data.get('chargingStatus', {})
        if charging_data:
            property_values.extend([
                create_property_value('IsCharging', charging_data.get('isCharging'), timestamp),
                create_property_value('ChargingPower', charging_data.get('chargingPower'), timestamp)
            ])
        
        # Send data to SiteWise
        if property_values:
            send_to_sitewise(asset_id, property_values)
            
        # Check for alerts
        check_vehicle_alerts(vehicle_id, data)
        
    except Exception as e:
        logger.error(f"Error processing vehicle telemetry for {vehicle_id}: {str(e)}")
        raise

def process_charging_station_data(station_id: str, data: Dict[str, Any], timestamp: str):
    """
    Process charging station data and send to SiteWise
    """
    try:
        # Find or create charging station asset
        asset_id = find_or_create_charging_station_asset(station_id, data)
        
        # Prepare property values
        property_values = []
        
        station_status = data.get('stationStatus', {})
        if station_status:
            property_values.extend([
                create_property_value('CurrentPower', station_status.get('currentPower'), timestamp),
                create_property_value('AvailableConnectors', station_status.get('availableConnectors'), timestamp),
                create_property_value('ActiveSessions', station_status.get('activeSessions'), timestamp),
                create_property_value('EnergyDelivered', station_status.get('energyDelivered'), timestamp),
                create_property_value('Temperature', station_status.get('temperature'), timestamp)
            ])
        
        # Send data to SiteWise
        if property_values:
            send_to_sitewise(asset_id, property_values)
            
    except Exception as e:
        logger.error(f"Error processing charging station data for {station_id}: {str(e)}")
        raise

def find_or_create_vehicle_asset(vehicle_id: str, data: Dict[str, Any]) -> str:
    """
    Find existing vehicle asset or create new one
    """
    try:
        # Try to find existing asset
        response = sitewise_client.list_assets(
            assetModelId=SITEWISE_VEHICLE_MODEL_ID,
            filter='ALL'
        )
        
        for asset in response.get('assetSummaries', []):
            if asset['name'] == vehicle_id:
                return asset['id']
        
        # Create new asset if not found
        vehicle_type = data.get('vehicleType', 'unknown')
        model_id = SITEWISE_EV_MODEL_ID if vehicle_type == 'electric' else SITEWISE_VEHICLE_MODEL_ID
        
        response = sitewise_client.create_asset(
            assetName=vehicle_id,
            assetModelId=model_id,
            assetDescription=f"Fleet vehicle {vehicle_id}"
        )
        
        logger.info(f"Created new vehicle asset: {vehicle_id}")
        return response['assetId']
        
    except Exception as e:
        logger.error(f"Error finding/creating vehicle asset {vehicle_id}: {str(e)}")
        raise

def find_or_create_charging_station_asset(station_id: str, data: Dict[str, Any]) -> str:
    """
    Find existing charging station asset or create new one
    """
    try:
        # Try to find existing asset
        response = sitewise_client.list_assets(
            assetModelId=SITEWISE_CHARGING_MODEL_ID,
            filter='ALL'
        )
        
        for asset in response.get('assetSummaries', []):
            if asset['name'] == station_id:
                return asset['id']
        
        # Create new asset if not found
        response = sitewise_client.create_asset(
            assetName=station_id,
            assetModelId=SITEWISE_CHARGING_MODEL_ID,
            assetDescription=f"Charging station {station_id}"
        )
        
        logger.info(f"Created new charging station asset: {station_id}")
        return response['assetId']
        
    except Exception as e:
        logger.error(f"Error finding/creating charging station asset {station_id}: {str(e)}")
        raise

def create_property_value(property_name: str, value: Any, timestamp: str) -> Dict[str, Any]:
    """
    Create SiteWise property value entry
    """
    if value is None:
        return None
    
    # Convert timestamp to epoch seconds
    if isinstance(timestamp, str):
        dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        timestamp_seconds = int(dt.timestamp())
    else:
        timestamp_seconds = int(timestamp)
    
    property_value = {
        'propertyAlias': f"/{property_name}",
        'propertyValues': [
            {
                'timestamp': {
                    'timeInSeconds': timestamp_seconds
                },
                'quality': 'GOOD'
            }
        ]
    }
    
    # Set value based on type
    if isinstance(value, bool):
        property_value['propertyValues'][0]['value'] = {'booleanValue': value}
    elif isinstance(value, int):
        property_value['propertyValues'][0]['value'] = {'integerValue': value}
    elif isinstance(value, float):
        property_value['propertyValues'][0]['value'] = {'doubleValue': value}
    else:
        property_value['propertyValues'][0]['value'] = {'stringValue': str(value)}
    
    return property_value

def send_to_sitewise(asset_id: str, property_values: List[Dict[str, Any]]):
    """
    Send property values to SiteWise
    """
    try:
        # Filter out None values
        valid_property_values = [pv for pv in property_values if pv is not None]
        
        if not valid_property_values:
            return
        
        # Batch put asset property values
        response = sitewise_client.batch_put_asset_property_value(
            entries=[
                {
                    'entryId': f"{asset_id}_{i}",
                    'assetId': asset_id,
                    **property_value
                }
                for i, property_value in enumerate(valid_property_values)
            ]
        )
        
        # Check for errors
        if response.get('errorEntries'):
            logger.warning(f"SiteWise batch put errors: {response['errorEntries']}")
        
        logger.info(f"Successfully sent {len(valid_property_values)} property values to SiteWise for asset {asset_id}")
        
    except Exception as e:
        logger.error(f"Error sending data to SiteWise: {str(e)}")
        raise

def check_vehicle_alerts(vehicle_id: str, data: Dict[str, Any]):
    """
    Check for alert conditions and send notifications
    """
    try:
        alerts = []
        
        # Engine temperature alert
        engine_temp = data.get('engineDiagnostics', {}).get('engineTemperature')
        if engine_temp and engine_temp > 110:
            alerts.append(f"High engine temperature: {engine_temp}°C")
        
        # Oil pressure alert
        oil_pressure = data.get('engineDiagnostics', {}).get('oilPressure')
        if oil_pressure and oil_pressure < 20:
            alerts.append(f"Low oil pressure: {oil_pressure} PSI")
        
        # Battery voltage alert
        battery_voltage = data.get('vehicleHealth', {}).get('batteryVoltage')
        if battery_voltage and battery_voltage < 11.5:
            alerts.append(f"Low battery voltage: {battery_voltage}V")
        
        # Maintenance score alert
        maintenance_score = data.get('vehicleHealth', {}).get('maintenanceScore')
        if maintenance_score and maintenance_score < 30:
            alerts.append(f"Low maintenance score: {maintenance_score}%")
        
        # EV battery alerts
        soc = data.get('batteryStatus', {}).get('stateOfCharge')
        if soc and soc < 10:
            alerts.append(f"Critical battery level: {soc}%")
        
        # Send alerts if any
        if alerts and SNS_TOPIC_ARN:
            message = f"Vehicle {vehicle_id} alerts:\n" + "\n".join(alerts)
            sns_client.publish(
                TopicArn=SNS_TOPIC_ARN,
                Message=message,
                Subject=f"Fleet Alert: Vehicle {vehicle_id}"
            )
            logger.info(f"Sent {len(alerts)} alerts for vehicle {vehicle_id}")
            
    except Exception as e:
        logger.error(f"Error checking alerts for vehicle {vehicle_id}: {str(e)}")
        # Don't raise here as alerts are not critical
