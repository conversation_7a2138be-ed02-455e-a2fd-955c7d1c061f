#!/usr/bin/env python3

import json
import time
import random
import boto3
import argparse
import logging
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor
import numpy as np

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VehicleSimulator:
    """
    Simulates vehicle telemetry data and sends it to AWS IoT Core
    """
    
    def __init__(self, region='us-east-1'):
        self.iot_client = boto3.client('iot-data', region_name=region)
        self.region = region
        
        # Vehicle simulation parameters
        self.vehicles = {}
        self.simulation_running = False
        
    def initialize_vehicle(self, vehicle_id, vehicle_type='electric'):
        """Initialize a vehicle with baseline parameters"""
        
        base_params = {
            'electric': {
                'batteryCapacity': random.uniform(70, 100),  # kWh
                'maxChargingPower': random.uniform(150, 250),  # kW
                'efficiency': random.uniform(15, 25),  # kWh/100km
                'range': random.uniform(300, 500)  # km
            },
            'ice': {
                'fuelTankCapacity': random.uniform(50, 80),  # L
                'fuelEfficiency': random.uniform(6, 12),  # L/100km
                'engineSize': random.uniform(1.5, 3.0)  # L
            }
        }
        
        vehicle = {
            'vehicleId': vehicle_id,
            'vehicleType': vehicle_type,
            'location': {
                'latitude': random.uniform(40.0, 41.0),
                'longitude': random.uniform(-74.5, -73.5),
                'altitude': random.uniform(0, 100),
                'heading': random.uniform(0, 360),
                'speed': 0.0
            },
            'engineDiagnostics': {
                'engineTemperature': random.uniform(80, 95),
                'oilPressure': random.uniform(30, 50),
                'rpm': 800,
                'fuelLevel': random.uniform(20, 100),
                'fuelConsumptionRate': 0.0,
                'engineLoad': random.uniform(10, 30)
            },
            'vehicleHealth': {
                'odometer': random.uniform(10000, 150000),
                'tirePressureFrontLeft': random.uniform(30, 35),
                'tirePressureFrontRight': random.uniform(30, 35),
                'tirePressureRearLeft': random.uniform(30, 35),
                'tirePressureRearRight': random.uniform(30, 35),
                'brakeWearLevel': random.uniform(20, 100),
                'batteryVoltage': random.uniform(12.0, 14.5),
                'maintenanceScore': random.uniform(70, 95),
                'anomalyScore': random.uniform(0, 20)
            },
            'status': 'active',
            'lastUpdate': datetime.now()
        }
        
        # Add vehicle-type specific parameters
        if vehicle_type in base_params:
            vehicle.update(base_params[vehicle_type])
        
        # Add EV-specific parameters
        if vehicle_type == 'electric':
            vehicle['batteryStatus'] = {
                'stateOfCharge': random.uniform(20, 90),
                'stateOfHealth': random.uniform(85, 100),
                'batteryTemperature': random.uniform(20, 40),
                'voltage': random.uniform(350, 400),
                'current': 0.0,
                'power': 0.0,
                'estimatedRange': random.uniform(50, 400),
                'energyConsumptionRate': random.uniform(15, 25),
                'regenerativeBrakingEnergy': 0.0
            }
            vehicle['chargingStatus'] = {
                'isCharging': False,
                'chargingPower': 0.0,
                'chargingVoltage': 0.0,
                'chargingCurrent': 0.0,
                'timeToFullCharge': 0,
                'chargingEfficiency': random.uniform(85, 95),
                'chargingCost': 0.0,
                'connectedStationId': None
            }
        
        self.vehicles[vehicle_id] = vehicle
        logger.info(f"Initialized vehicle {vehicle_id} ({vehicle_type})")
        
    def simulate_driving(self, vehicle_id):
        """Simulate driving behavior for a vehicle"""
        
        if vehicle_id not in self.vehicles:
            return
        
        vehicle = self.vehicles[vehicle_id]
        
        # Simulate movement
        speed_change = random.uniform(-5, 10)
        vehicle['location']['speed'] = max(0, min(120, vehicle['location']['speed'] + speed_change))
        
        if vehicle['location']['speed'] > 0:
            # Update location based on speed and heading
            distance = vehicle['location']['speed'] * (30 / 3600)  # 30 seconds in hours
            lat_change = distance * np.cos(np.radians(vehicle['location']['heading'])) / 111.32
            lon_change = distance * np.sin(np.radians(vehicle['location']['heading'])) / (111.32 * np.cos(np.radians(vehicle['location']['latitude'])))
            
            vehicle['location']['latitude'] += lat_change
            vehicle['location']['longitude'] += lon_change
            
            # Occasionally change heading
            if random.random() < 0.1:
                vehicle['location']['heading'] += random.uniform(-30, 30)
                vehicle['location']['heading'] = vehicle['location']['heading'] % 360
        
        # Simulate engine diagnostics
        if vehicle['location']['speed'] > 0:
            vehicle['engineDiagnostics']['rpm'] = random.uniform(1500, 4000)
            vehicle['engineDiagnostics']['engineLoad'] = random.uniform(30, 80)
            vehicle['engineDiagnostics']['engineTemperature'] = random.uniform(85, 105)
            vehicle['engineDiagnostics']['fuelConsumptionRate'] = random.uniform(5, 15)
        else:
            vehicle['engineDiagnostics']['rpm'] = random.uniform(700, 900)
            vehicle['engineDiagnostics']['engineLoad'] = random.uniform(10, 30)
            vehicle['engineDiagnostics']['engineTemperature'] = random.uniform(80, 95)
            vehicle['engineDiagnostics']['fuelConsumptionRate'] = random.uniform(0.5, 2.0)
        
        # Simulate wear and tear
        vehicle['vehicleHealth']['odometer'] += vehicle['location']['speed'] * (30 / 3600)
        
        # Occasionally introduce anomalies
        if random.random() < 0.05:  # 5% chance
            self.introduce_anomaly(vehicle)
        
        # EV-specific simulation
        if vehicle.get('vehicleType') == 'electric':
            self.simulate_ev_behavior(vehicle)
        
        vehicle['lastUpdate'] = datetime.now()
    
    def simulate_ev_behavior(self, vehicle):
        """Simulate EV-specific behavior"""
        
        # Battery consumption based on speed and conditions
        if vehicle['location']['speed'] > 0:
            consumption_rate = vehicle.get('efficiency', 20) * (vehicle['location']['speed'] / 100)
            energy_consumed = consumption_rate * (30 / 3600)  # 30 seconds
            
            # Update state of charge
            capacity = vehicle.get('batteryCapacity', 75)
            soc_decrease = (energy_consumed / capacity) * 100
            vehicle['batteryStatus']['stateOfCharge'] = max(0, vehicle['batteryStatus']['stateOfCharge'] - soc_decrease)
            
            # Update range estimate
            remaining_energy = (vehicle['batteryStatus']['stateOfCharge'] / 100) * capacity
            vehicle['batteryStatus']['estimatedRange'] = (remaining_energy / vehicle.get('efficiency', 20)) * 100
        
        # Simulate charging if SoC is low and not moving
        if vehicle['batteryStatus']['stateOfCharge'] < 30 and vehicle['location']['speed'] == 0:
            if random.random() < 0.3:  # 30% chance to start charging
                vehicle['chargingStatus']['isCharging'] = True
                vehicle['chargingStatus']['chargingPower'] = random.uniform(50, vehicle.get('maxChargingPower', 150))
                vehicle['chargingStatus']['connectedStationId'] = f"station-{random.randint(1, 10):03d}"
        
        # Simulate charging process
        if vehicle['chargingStatus']['isCharging']:
            charging_power = vehicle['chargingStatus']['chargingPower']
            capacity = vehicle.get('batteryCapacity', 75)
            
            # Add charge (30 seconds of charging)
            energy_added = charging_power * (30 / 3600)  # kWh
            soc_increase = (energy_added / capacity) * 100
            vehicle['batteryStatus']['stateOfCharge'] = min(100, vehicle['batteryStatus']['stateOfCharge'] + soc_increase)
            
            # Stop charging when full or randomly
            if vehicle['batteryStatus']['stateOfCharge'] >= 95 or random.random() < 0.1:
                vehicle['chargingStatus']['isCharging'] = False
                vehicle['chargingStatus']['chargingPower'] = 0.0
                vehicle['chargingStatus']['connectedStationId'] = None
    
    def introduce_anomaly(self, vehicle):
        """Introduce random anomalies for testing"""
        
        anomaly_type = random.choice([
            'high_temperature', 'low_oil_pressure', 'tire_pressure_low',
            'battery_voltage_low', 'high_consumption'
        ])
        
        if anomaly_type == 'high_temperature':
            vehicle['engineDiagnostics']['engineTemperature'] = random.uniform(110, 130)
            vehicle['vehicleHealth']['anomalyScore'] = random.uniform(70, 95)
        elif anomaly_type == 'low_oil_pressure':
            vehicle['engineDiagnostics']['oilPressure'] = random.uniform(10, 20)
            vehicle['vehicleHealth']['anomalyScore'] = random.uniform(60, 85)
        elif anomaly_type == 'tire_pressure_low':
            tire = random.choice(['tirePressureFrontLeft', 'tirePressureFrontRight', 
                                'tirePressureRearLeft', 'tirePressureRearRight'])
            vehicle['vehicleHealth'][tire] = random.uniform(15, 25)
            vehicle['vehicleHealth']['anomalyScore'] = random.uniform(50, 75)
        elif anomaly_type == 'battery_voltage_low':
            vehicle['vehicleHealth']['batteryVoltage'] = random.uniform(10.5, 11.5)
            vehicle['vehicleHealth']['anomalyScore'] = random.uniform(40, 70)
        elif anomaly_type == 'high_consumption':
            vehicle['engineDiagnostics']['fuelConsumptionRate'] *= random.uniform(1.5, 2.5)
            vehicle['vehicleHealth']['anomalyScore'] = random.uniform(55, 80)
        
        logger.info(f"Introduced anomaly '{anomaly_type}' for vehicle {vehicle['vehicleId']}")
    
    def send_telemetry(self, vehicle_id):
        """Send telemetry data to AWS IoT Core"""
        
        if vehicle_id not in self.vehicles:
            return
        
        vehicle = self.vehicles[vehicle_id]
        
        # Prepare telemetry payload
        telemetry = {
            'vehicleId': vehicle_id,
            'timestamp': datetime.now().isoformat(),
            'location': vehicle['location'],
            'engineDiagnostics': vehicle['engineDiagnostics'],
            'vehicleHealth': vehicle['vehicleHealth']
        }
        
        # Add EV-specific data
        if vehicle.get('vehicleType') == 'electric':
            telemetry['batteryStatus'] = vehicle['batteryStatus']
            telemetry['chargingStatus'] = vehicle['chargingStatus']
        
        try:
            # Send to IoT Core
            topic = f"fleet/vehicles/{vehicle_id}/telemetry"
            
            response = self.iot_client.publish(
                topic=topic,
                qos=1,
                payload=json.dumps(telemetry)
            )
            
            logger.debug(f"Sent telemetry for {vehicle_id} to topic {topic}")
            
        except Exception as e:
            logger.error(f"Failed to send telemetry for {vehicle_id}: {str(e)}")
    
    def simulate_vehicle(self, vehicle_id):
        """Simulate a single vehicle continuously"""
        
        while self.simulation_running:
            try:
                self.simulate_driving(vehicle_id)
                self.send_telemetry(vehicle_id)
                time.sleep(30)  # Send telemetry every 30 seconds
                
            except Exception as e:
                logger.error(f"Error simulating vehicle {vehicle_id}: {str(e)}")
                time.sleep(5)
    
    def start_simulation(self, vehicle_ids, duration_minutes=None):
        """Start simulation for multiple vehicles"""
        
        logger.info(f"Starting simulation for {len(vehicle_ids)} vehicles")
        self.simulation_running = True
        
        # Initialize vehicles
        for vehicle_id in vehicle_ids:
            vehicle_type = 'electric' if random.random() < 0.7 else 'ice'  # 70% electric
            self.initialize_vehicle(vehicle_id, vehicle_type)
        
        # Start simulation threads
        with ThreadPoolExecutor(max_workers=len(vehicle_ids)) as executor:
            futures = [executor.submit(self.simulate_vehicle, vid) for vid in vehicle_ids]
            
            try:
                if duration_minutes:
                    time.sleep(duration_minutes * 60)
                    self.simulation_running = False
                else:
                    # Run indefinitely until interrupted
                    while True:
                        time.sleep(1)
                        
            except KeyboardInterrupt:
                logger.info("Simulation interrupted by user")
                self.simulation_running = False
            
            # Wait for all threads to complete
            for future in futures:
                future.result()
        
        logger.info("Simulation completed")

def main():
    parser = argparse.ArgumentParser(description='Fleet Vehicle Simulator for AWS IoT')
    parser.add_argument('--vehicles', type=int, default=5, help='Number of vehicles to simulate')
    parser.add_argument('--duration', type=int, help='Simulation duration in minutes (default: run indefinitely)')
    parser.add_argument('--region', default='us-east-1', help='AWS region')
    parser.add_argument('--prefix', default='vehicle', help='Vehicle ID prefix')
    
    args = parser.parse_args()
    
    # Generate vehicle IDs
    vehicle_ids = [f"{args.prefix}-{i:03d}" for i in range(1, args.vehicles + 1)]
    
    # Create simulator
    simulator = VehicleSimulator(region=args.region)
    
    # Start simulation
    try:
        simulator.start_simulation(vehicle_ids, args.duration)
    except Exception as e:
        logger.error(f"Simulation failed: {str(e)}")

if __name__ == "__main__":
    main()
