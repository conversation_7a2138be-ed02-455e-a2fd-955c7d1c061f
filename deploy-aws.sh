#!/bin/bash

# Fleet Management Digital Twins - AWS Deployment Script
# This script deploys the complete fleet management solution on AWS

set -e

# Configuration
AWS_REGION=${AWS_REGION:-us-east-1}
STACK_NAME=${STACK_NAME:-fleet-management}
WORKSPACE_NAME=${WORKSPACE_NAME:-fleet-management-workspace}
ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)

echo "🚀 Starting Fleet Management Digital Twins deployment on AWS"
echo "Region: $AWS_REGION"
echo "Account ID: $ACCOUNT_ID"
echo "Stack Name: $STACK_NAME"

# Check prerequisites
echo "📋 Checking prerequisites..."

# Check AWS CLI
if ! command -v aws &> /dev/null; then
    echo "❌ AWS CLI is not installed. Please install it first."
    exit 1
fi

# Check CDK
if ! command -v cdk &> /dev/null; then
    echo "❌ AWS CDK is not installed. Installing..."
    npm install -g aws-cdk
fi

# Check Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install it first."
    exit 1
fi

echo "✅ Prerequisites check completed"

# Step 1: Bootstrap CDK (if not already done)
echo "🔧 Bootstrapping CDK..."
cdk bootstrap aws://$ACCOUNT_ID/$AWS_REGION

# Step 2: Install CDK dependencies
echo "📦 Installing CDK dependencies..."
cd aws-cdk
python3 -m pip install -r requirements.txt
cd ..

# Step 3: Deploy infrastructure with CDK
echo "🏗️ Deploying infrastructure with CDK..."
cd aws-cdk
cdk deploy --require-approval never
cd ..

# Step 4: Create IoT TwinMaker workspace
echo "🔗 Creating IoT TwinMaker workspace..."

# Create TwinMaker execution role
aws iam create-role \
    --role-name TwinMakerExecutionRole \
    --assume-role-policy-document file://aws-config/twinmaker-trust-policy.json \
    --region $AWS_REGION || echo "Role may already exist"

aws iam attach-role-policy \
    --role-name TwinMakerExecutionRole \
    --policy-arn arn:aws:iam::aws:policy/AWSIoTTwinMakerServiceRolePolicy \
    --region $AWS_REGION || echo "Policy may already be attached"

# Create TwinMaker workspace
aws iottwinmaker create-workspace \
    --workspace-id $WORKSPACE_NAME \
    --description "Fleet Management Digital Twins Workspace" \
    --s3-location s3://fleet-twinmaker-workspace-$ACCOUNT_ID \
    --role arn:aws:iam::$ACCOUNT_ID:role/TwinMakerExecutionRole \
    --region $AWS_REGION || echo "Workspace may already exist"

# Step 5: Create TwinMaker component types
echo "🧩 Creating TwinMaker component types..."

aws iottwinmaker create-component-type \
    --workspace-id $WORKSPACE_NAME \
    --component-type-id VehicleComponent \
    --description "Vehicle digital twin component" \
    --property-definitions file://aws-config/vehicle-properties.json \
    --region $AWS_REGION || echo "Component type may already exist"

aws iottwinmaker create-component-type \
    --workspace-id $WORKSPACE_NAME \
    --component-type-id ElectricVehicleComponent \
    --description "Electric vehicle digital twin component" \
    --extends-from VehicleComponent \
    --property-definitions file://aws-config/ev-properties.json \
    --region $AWS_REGION || echo "Component type may already exist"

aws iottwinmaker create-component-type \
    --workspace-id $WORKSPACE_NAME \
    --component-type-id DriverComponent \
    --description "Driver digital twin component" \
    --property-definitions file://aws-config/driver-properties.json \
    --region $AWS_REGION || echo "Component type may already exist"

# Step 6: Deploy Lambda functions
echo "⚡ Deploying Lambda functions..."

# Package and deploy predictive maintenance Lambda
cd aws-lambda/predictive-maintenance
zip -r predictive-maintenance.zip . -x "*.pyc" "__pycache__/*"
aws s3 cp predictive-maintenance.zip s3://fleet-data-lake-$ACCOUNT_ID/lambda-functions/
rm predictive-maintenance.zip

aws lambda update-function-code \
    --function-name fleet-predictive-maintenance \
    --s3-bucket fleet-data-lake-$ACCOUNT_ID \
    --s3-key lambda-functions/predictive-maintenance.zip \
    --region $AWS_REGION || echo "Function may not exist yet"

cd ../..

# Package and deploy anomaly detection Lambda
cd aws-lambda/anomaly-detection
zip -r anomaly-detection.zip . -x "*.pyc" "__pycache__/*"
aws s3 cp anomaly-detection.zip s3://fleet-data-lake-$ACCOUNT_ID/lambda-functions/
rm anomaly-detection.zip

aws lambda update-function-code \
    --function-name fleet-anomaly-detection \
    --s3-bucket fleet-data-lake-$ACCOUNT_ID \
    --s3-key lambda-functions/anomaly-detection.zip \
    --region $AWS_REGION || echo "Function may not exist yet"

cd ../..

# Step 7: Train and deploy SageMaker models
echo "🤖 Setting up SageMaker models..."

# Create SageMaker execution role
aws iam create-role \
    --role-name SageMakerExecutionRole \
    --assume-role-policy-document '{
        "Version": "2012-10-17",
        "Statement": [{
            "Effect": "Allow",
            "Principal": {"Service": "sagemaker.amazonaws.com"},
            "Action": "sts:AssumeRole"
        }]
    }' \
    --region $AWS_REGION || echo "Role may already exist"

aws iam attach-role-policy \
    --role-name SageMakerExecutionRole \
    --policy-arn arn:aws:iam::aws:policy/AmazonSageMakerFullAccess \
    --region $AWS_REGION || echo "Policy may already be attached"

# Upload training code to S3
aws s3 cp sagemaker/ s3://fleet-data-lake-$ACCOUNT_ID/sagemaker-code/ --recursive

echo "📊 SageMaker setup completed. You can now train models using the uploaded code."

# Step 8: Create sample IoT devices
echo "📱 Creating sample IoT devices..."

# Create sample vehicles
for i in {1..5}; do
    VEHICLE_ID=$(printf "vehicle-%03d" $i)
    
    # Create IoT thing
    aws iot create-thing \
        --thing-name $VEHICLE_ID \
        --thing-type-name FleetVehicle \
        --region $AWS_REGION || echo "Thing $VEHICLE_ID may already exist"
    
    # Create and attach certificate
    CERT_OUTPUT=$(aws iot create-keys-and-certificate \
        --set-as-active \
        --region $AWS_REGION)
    
    CERT_ARN=$(echo $CERT_OUTPUT | jq -r '.certificateArn')
    
    if [ "$CERT_ARN" != "null" ]; then
        # Attach policy to certificate
        aws iot attach-policy \
            --policy-name FleetDevicePolicy \
            --target $CERT_ARN \
            --region $AWS_REGION
        
        # Attach certificate to thing
        aws iot attach-thing-principal \
            --thing-name $VEHICLE_ID \
            --principal $CERT_ARN \
            --region $AWS_REGION
        
        echo "✅ Created IoT device: $VEHICLE_ID"
    fi
done

# Step 9: Create TwinMaker entities
echo "🔗 Creating TwinMaker entities..."

# Create fleet entity
aws iottwinmaker create-entity \
    --workspace-id $WORKSPACE_NAME \
    --entity-id "fleet-001" \
    --entity-name "Main Fleet" \
    --description "Main fleet digital twin" \
    --region $AWS_REGION || echo "Entity may already exist"

# Create sample vehicle entities
for i in {1..5}; do
    VEHICLE_ID=$(printf "vehicle-%03d" $i)
    
    aws iottwinmaker create-entity \
        --workspace-id $WORKSPACE_NAME \
        --entity-id $VEHICLE_ID \
        --entity-name "Vehicle $i" \
        --description "Vehicle $i digital twin" \
        --components '{
            "VehicleComponent": {
                "componentTypeId": "VehicleComponent",
                "properties": {
                    "vehicleId": {"value": {"stringValue": "'$VEHICLE_ID'"}},
                    "make": {"value": {"stringValue": "Tesla"}},
                    "model": {"value": {"stringValue": "Model 3"}},
                    "year": {"value": {"integerValue": 2023}},
                    "vehicleType": {"value": {"stringValue": "car"}},
                    "powertrainType": {"value": {"stringValue": "electric"}},
                    "status": {"value": {"stringValue": "active"}}
                }
            }
        }' \
        --region $AWS_REGION || echo "Entity $VEHICLE_ID may already exist"
done

# Step 10: Set up monitoring and alerts
echo "📊 Setting up monitoring and alerts..."

# Create CloudWatch dashboard
aws cloudwatch put-dashboard \
    --dashboard-name "FleetManagementDashboard" \
    --dashboard-body '{
        "widgets": [
            {
                "type": "metric",
                "properties": {
                    "metrics": [
                        ["AWS/Kinesis", "IncomingRecords", "StreamName", "fleet-vehicle-telemetry"],
                        ["AWS/Lambda", "Invocations", "FunctionName", "fleet-predictive-maintenance"],
                        ["AWS/Lambda", "Errors", "FunctionName", "fleet-anomaly-detection"]
                    ],
                    "period": 300,
                    "stat": "Sum",
                    "region": "'$AWS_REGION'",
                    "title": "Fleet Management Metrics"
                }
            }
        ]
    }' \
    --region $AWS_REGION

echo "✅ Deployment completed successfully!"
echo ""
echo "🎉 Fleet Management Digital Twins is now deployed on AWS!"
echo ""
echo "📋 Next Steps:"
echo "1. Configure your IoT devices to send data to the endpoints"
echo "2. Train the SageMaker models with your historical data"
echo "3. Set up QuickSight dashboards for visualization"
echo "4. Configure SNS subscriptions for alerts"
echo ""
echo "📊 Resources Created:"
echo "- TwinMaker Workspace: $WORKSPACE_NAME"
echo "- S3 Buckets: fleet-twinmaker-workspace-$ACCOUNT_ID, fleet-data-lake-$ACCOUNT_ID"
echo "- Timestream Database: FleetManagementDB"
echo "- Kinesis Streams: fleet-vehicle-telemetry, fleet-driver-behavior, fleet-charging-data"
echo "- Lambda Functions: fleet-predictive-maintenance, fleet-anomaly-detection"
echo "- IoT Things: vehicle-001 to vehicle-005"
echo ""
echo "🔗 Useful Commands:"
echo "- View TwinMaker workspace: aws iottwinmaker get-workspace --workspace-id $WORKSPACE_NAME"
echo "- List entities: aws iottwinmaker list-entities --workspace-id $WORKSPACE_NAME"
echo "- Monitor Kinesis: aws kinesis describe-stream --stream-name fleet-vehicle-telemetry"
echo "- Check Lambda logs: aws logs describe-log-groups --log-group-name-prefix /aws/lambda/fleet"
