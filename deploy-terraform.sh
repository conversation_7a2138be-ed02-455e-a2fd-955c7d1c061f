#!/bin/bash

# Fleet Management Digital Twins - Terraform Deployment Script
# This script deploys the complete fleet management solution on AWS using Terraform

set -e

# Configuration
AWS_REGION=${AWS_REGION:-us-east-1}
ENVIRONMENT=${ENVIRONMENT:-dev}
PROJECT_NAME=${PROJECT_NAME:-fleet-management}
WORKSPACE_NAME=${WORKSPACE_NAME:-fleet-management-workspace}
TERRAFORM_VERSION=${TERRAFORM_VERSION:-1.5.0}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."

    # Check AWS CLI
    if ! command -v aws &> /dev/null; then
        log_error "AWS CLI is not installed. Please install it first."
        exit 1
    fi

    # Check AWS credentials
    if ! aws sts get-caller-identity &> /dev/null; then
        log_error "AWS credentials not configured. Please run 'aws configure' first."
        exit 1
    fi

    # Check Terraform
    if ! command -v terraform &> /dev/null; then
        log_warning "Terraform is not installed. Installing..."
        install_terraform
    else
        CURRENT_VERSION=$(terraform version -json | jq -r '.terraform_version')
        log_info "Terraform version: $CURRENT_VERSION"
    fi

    # Check jq
    if ! command -v jq &> /dev/null; then
        log_warning "jq is not installed. Installing..."
        if [[ "$OSTYPE" == "darwin"* ]]; then
            brew install jq
        elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
            sudo apt-get update && sudo apt-get install -y jq
        fi
    fi

    # Check Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 is not installed. Please install it first."
        exit 1
    fi

    log_success "Prerequisites check completed"
}

# Function to install Terraform
install_terraform() {
    log_info "Installing Terraform..."
    
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        brew tap hashicorp/tap
        brew install hashicorp/tap/terraform
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        wget -O- https://apt.releases.hashicorp.com/gpg | gpg --dearmor | sudo tee /usr/share/keyrings/hashicorp-archive-keyring.gpg
        echo "deb [signed-by=/usr/share/keyrings/hashicorp-archive-keyring.gpg] https://apt.releases.hashicorp.com $(lsb_release -cs) main" | sudo tee /etc/apt/sources.list.d/hashicorp.list
        sudo apt update && sudo apt install terraform
    else
        log_error "Unsupported operating system. Please install Terraform manually."
        exit 1
    fi
    
    log_success "Terraform installed successfully"
}

# Function to prepare Lambda packages
prepare_lambda_packages() {
    log_info "Preparing Lambda function packages..."
    
    # Create lambda-packages directory
    mkdir -p terraform/lambda-packages
    
    # Create a simple telemetry processor if it doesn't exist
    if [ ! -d "aws-lambda/telemetry-processor" ]; then
        log_info "Creating sample telemetry processor..."
        mkdir -p aws-lambda/telemetry-processor
        cat > aws-lambda/telemetry-processor/lambda_function.py << 'EOF'
import json
import boto3
import os
from datetime import datetime

def lambda_handler(event, context):
    """
    Simple telemetry processor for demonstration
    """
    print(f"Received event: {json.dumps(event)}")
    
    # Process records
    for record in event.get('Records', []):
        if 'kinesis' in record:
            # Decode Kinesis data
            import base64
            payload = base64.b64decode(record['kinesis']['data'])
            data = json.loads(payload)
            print(f"Processing telemetry data: {data}")
    
    return {
        'statusCode': 200,
        'body': json.dumps('Telemetry processed successfully')
    }
EOF
    fi
    
    log_success "Lambda packages prepared"
}

# Function to create terraform.tfvars if it doesn't exist
create_tfvars() {
    if [ ! -f "terraform/terraform.tfvars" ]; then
        log_info "Creating terraform.tfvars from example..."
        cp terraform/terraform.tfvars.example terraform/terraform.tfvars
        
        # Update with current values
        sed -i.bak "s/aws_region   = \"us-east-1\"/aws_region   = \"$AWS_REGION\"/" terraform/terraform.tfvars
        sed -i.bak "s/environment  = \"dev\"/environment  = \"$ENVIRONMENT\"/" terraform/terraform.tfvars
        sed -i.bak "s/project_name = \"fleet-management\"/project_name = \"$PROJECT_NAME\"/" terraform/terraform.tfvars
        
        rm terraform/terraform.tfvars.bak
        
        log_warning "Please review and customize terraform/terraform.tfvars before proceeding"
        read -p "Press Enter to continue or Ctrl+C to exit..."
    fi
}

# Function to initialize Terraform
init_terraform() {
    log_info "Initializing Terraform..."
    
    cd terraform
    
    # Initialize Terraform
    terraform init
    
    # Create or select workspace
    if terraform workspace list | grep -q "$ENVIRONMENT"; then
        terraform workspace select "$ENVIRONMENT"
    else
        terraform workspace new "$ENVIRONMENT"
    fi
    
    log_success "Terraform initialized successfully"
}

# Function to plan Terraform deployment
plan_terraform() {
    log_info "Planning Terraform deployment..."
    
    terraform plan -var="environment=$ENVIRONMENT" -out=tfplan
    
    log_success "Terraform plan completed"
    log_warning "Please review the plan above before proceeding"
    read -p "Do you want to continue with the deployment? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Deployment cancelled by user"
        exit 0
    fi
}

# Function to apply Terraform deployment
apply_terraform() {
    log_info "Applying Terraform deployment..."
    
    terraform apply tfplan
    
    log_success "Terraform deployment completed successfully"
}

# Function to create TwinMaker workspace
create_twinmaker_workspace() {
    log_info "Creating IoT TwinMaker workspace..."
    
    # Get outputs from Terraform
    TWINMAKER_BUCKET=$(terraform output -raw twinmaker_bucket_name)
    TWINMAKER_ROLE_ARN=$(terraform output -raw twinmaker_execution_role_arn)
    
    # Create TwinMaker workspace
    aws iottwinmaker create-workspace \
        --workspace-id "$WORKSPACE_NAME" \
        --description "Fleet Management Digital Twins Workspace" \
        --s3-location "s3://$TWINMAKER_BUCKET" \
        --role "$TWINMAKER_ROLE_ARN" \
        --region "$AWS_REGION" || log_warning "Workspace may already exist"
    
    log_success "TwinMaker workspace created"
}

# Function to create TwinMaker component types
create_component_types() {
    log_info "Creating TwinMaker component types..."
    
    # Create Vehicle component type
    aws iottwinmaker create-component-type \
        --workspace-id "$WORKSPACE_NAME" \
        --component-type-id VehicleComponent \
        --description "Vehicle digital twin component" \
        --property-definitions file://../aws-config/vehicle-properties.json \
        --region "$AWS_REGION" || log_warning "Component type may already exist"
    
    # Create EV component type
    aws iottwinmaker create-component-type \
        --workspace-id "$WORKSPACE_NAME" \
        --component-type-id ElectricVehicleComponent \
        --description "Electric vehicle digital twin component" \
        --extends-from VehicleComponent \
        --property-definitions file://../aws-config/ev-properties.json \
        --region "$AWS_REGION" || log_warning "Component type may already exist"
    
    # Create Driver component type
    aws iottwinmaker create-component-type \
        --workspace-id "$WORKSPACE_NAME" \
        --component-type-id DriverComponent \
        --description "Driver digital twin component" \
        --property-definitions file://../aws-config/driver-properties.json \
        --region "$AWS_REGION" || log_warning "Component type may already exist"
    
    log_success "TwinMaker component types created"
}

# Function to create sample IoT devices
create_sample_devices() {
    log_info "Creating sample IoT devices..."
    
    DEVICE_POLICY=$(terraform output -raw iot_device_policy_name)
    
    # Create sample vehicles
    for i in {1..5}; do
        VEHICLE_ID=$(printf "vehicle-%03d" $i)
        
        # Create IoT thing
        aws iot create-thing \
            --thing-name "$VEHICLE_ID" \
            --thing-type-name FleetVehicle \
            --region "$AWS_REGION" || log_warning "Thing $VEHICLE_ID may already exist"
        
        # Create and attach certificate
        CERT_OUTPUT=$(aws iot create-keys-and-certificate \
            --set-as-active \
            --region "$AWS_REGION" 2>/dev/null || echo '{}')
        
        CERT_ARN=$(echo "$CERT_OUTPUT" | jq -r '.certificateArn // empty')
        
        if [ -n "$CERT_ARN" ] && [ "$CERT_ARN" != "null" ]; then
            # Attach policy to certificate
            aws iot attach-policy \
                --policy-name "$DEVICE_POLICY" \
                --target "$CERT_ARN" \
                --region "$AWS_REGION"
            
            # Attach certificate to thing
            aws iot attach-thing-principal \
                --thing-name "$VEHICLE_ID" \
                --principal "$CERT_ARN" \
                --region "$AWS_REGION"
            
            log_success "Created IoT device: $VEHICLE_ID"
        fi
    done
}

# Function to create TwinMaker entities
create_twinmaker_entities() {
    log_info "Creating TwinMaker entities..."
    
    # Create fleet entity
    aws iottwinmaker create-entity \
        --workspace-id "$WORKSPACE_NAME" \
        --entity-id "fleet-001" \
        --entity-name "Main Fleet" \
        --description "Main fleet digital twin" \
        --region "$AWS_REGION" || log_warning "Entity may already exist"
    
    # Create sample vehicle entities
    for i in {1..5}; do
        VEHICLE_ID=$(printf "vehicle-%03d" $i)
        
        aws iottwinmaker create-entity \
            --workspace-id "$WORKSPACE_NAME" \
            --entity-id "$VEHICLE_ID" \
            --entity-name "Vehicle $i" \
            --description "Vehicle $i digital twin" \
            --components '{
                "VehicleComponent": {
                    "componentTypeId": "VehicleComponent",
                    "properties": {
                        "vehicleId": {"value": {"stringValue": "'$VEHICLE_ID'"}},
                        "make": {"value": {"stringValue": "Tesla"}},
                        "model": {"value": {"stringValue": "Model 3"}},
                        "year": {"value": {"integerValue": 2023}},
                        "vehicleType": {"value": {"stringValue": "car"}},
                        "powertrainType": {"value": {"stringValue": "electric"}},
                        "status": {"value": {"stringValue": "active"}}
                    }
                }
            }' \
            --region "$AWS_REGION" || log_warning "Entity $VEHICLE_ID may already exist"
    done
    
    log_success "TwinMaker entities created"
}

# Function to display deployment summary
display_summary() {
    log_success "🎉 Fleet Management Digital Twins deployment completed successfully!"
    echo
    echo "📋 Deployment Summary:"
    echo "======================"
    echo "Region: $AWS_REGION"
    echo "Environment: $ENVIRONMENT"
    echo "Project: $PROJECT_NAME"
    echo "TwinMaker Workspace: $WORKSPACE_NAME"
    echo
    echo "📊 Resources Created:"
    echo "- S3 Buckets: $(terraform output -raw twinmaker_bucket_name), $(terraform output -raw data_lake_bucket_name)"
    echo "- Timestream Database: $(terraform output -raw timestream_database_name)"
    echo "- Kinesis Streams: $(terraform output -raw vehicle_telemetry_stream_name), $(terraform output -raw driver_behavior_stream_name), $(terraform output -raw charging_data_stream_name)"
    echo "- Lambda Functions: $(terraform output -raw telemetry_processor_function_name), $(terraform output -raw predictive_maintenance_function_name), $(terraform output -raw anomaly_detection_function_name)"
    echo "- IoT Things: vehicle-001 to vehicle-005"
    echo
    echo "🔗 Useful Commands:"
    echo "- View TwinMaker workspace: aws iottwinmaker get-workspace --workspace-id $WORKSPACE_NAME"
    echo "- List entities: aws iottwinmaker list-entities --workspace-id $WORKSPACE_NAME"
    echo "- Monitor Kinesis: aws kinesis describe-stream --stream-name $(terraform output -raw vehicle_telemetry_stream_name)"
    echo "- Check Lambda logs: aws logs describe-log-groups --log-group-name-prefix /aws/lambda/fleet"
    echo
    echo "🧪 Next Steps:"
    echo "1. Test the deployment with the vehicle simulator: python3 aws-testing/vehicle_simulator.py"
    echo "2. Configure SNS subscriptions for alerts"
    echo "3. Set up QuickSight dashboards for visualization"
    echo "4. Train SageMaker models with your historical data"
}

# Main execution
main() {
    echo "🚀 Starting Fleet Management Digital Twins deployment with Terraform"
    echo "Region: $AWS_REGION"
    echo "Environment: $ENVIRONMENT"
    echo "Project: $PROJECT_NAME"
    echo

    check_prerequisites
    prepare_lambda_packages
    create_tfvars
    init_terraform
    plan_terraform
    apply_terraform
    
    cd ..  # Return to root directory
    
    create_twinmaker_workspace
    create_component_types
    create_sample_devices
    create_twinmaker_entities
    
    display_summary
}

# Handle script interruption
trap 'log_error "Deployment interrupted"; exit 1' INT TERM

# Run main function
main "$@"
