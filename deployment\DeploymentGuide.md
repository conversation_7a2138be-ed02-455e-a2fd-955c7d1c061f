# Fleet Management Digital Twins Deployment Guide

## Overview

This guide provides step-by-step instructions for deploying the comprehensive fleet management digital twins solution with AI capabilities.

## Prerequisites

### Infrastructure Requirements
- **Cloud Platform**: Azure, AWS, or Google Cloud
- **Digital Twins Platform**: Azure Digital Twins (recommended) or compatible DTDL platform
- **IoT Platform**: Azure IoT Hub, AWS IoT Core, or Google Cloud IoT Core
- **AI/ML Platform**: Azure Machine Learning, AWS SageMaker, or Google AI Platform
- **Analytics Platform**: Azure Synapse, AWS Redshift, or Google BigQuery
- **Visualization**: Power BI, Tableau, or custom dashboards

### Technical Prerequisites
- DTDL v3 compatible platform
- Real-time data streaming capabilities
- Machine learning model deployment infrastructure
- Time-series database for telemetry data
- Event processing capabilities

## Deployment Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   IoT Devices   │───▶│   IoT Platform  │───▶│ Digital Twins   │
│                 │    │                 │    │   Platform      │
│ • Vehicles      │    │ • Data Ingestion│    │                 │
│ • Charging      │    │ • Device Mgmt   │    │ • DTDL Models   │
│ • Sensors       │    │ • Security      │    │ • Relationships │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                                                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   AI/ML Models  │◀───│  Analytics      │◀───│  Data Storage   │
│                 │    │   Platform      │    │                 │
│ • Predictive    │    │                 │    │ • Time Series   │
│ • Anomaly Det.  │    │ • Real-time     │    │ • Historical    │
│ • Optimization  │    │ • Batch         │    │ • Model Data    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Step-by-Step Deployment

### Phase 1: Platform Setup

#### 1.1 Azure Digital Twins Setup (Recommended)
```bash
# Create resource group
az group create --name FleetManagementDT --location eastus

# Create Azure Digital Twins instance
az dt create --dt-name fleet-management-dt --resource-group FleetManagementDT

# Create IoT Hub
az iot hub create --name fleet-iot-hub --resource-group FleetManagementDT --sku S1
```

#### 1.2 Upload DTDL Models
```bash
# Upload core models
az dt model create --dt-name fleet-management-dt --models @models/Fleet.json
az dt model create --dt-name fleet-management-dt --models @models/Vehicle.json
az dt model create --dt-name fleet-management-dt --models @models/ElectricVehicle.json
az dt model create --dt-name fleet-management-dt --models @models/Driver.json
az dt model create --dt-name fleet-management-dt --models @models/Route.json
az dt model create --dt-name fleet-management-dt --models @models/ChargingStation.json
az dt model create --dt-name fleet-management-dt --models @models/Depot.json
az dt model create --dt-name fleet-management-dt --models @models/MaintenanceSchedule.json

# Upload AI/Analytics models
az dt model create --dt-name fleet-management-dt --models @models/PredictiveMaintenanceModel.json
az dt model create --dt-name fleet-management-dt --models @models/AnomalyDetectionModel.json
az dt model create --dt-name fleet-management-dt --models @models/EVChargeOptimizer.json
az dt model create --dt-name fleet-management-dt --models @models/FleetAnalytics.json
```

### Phase 2: Data Ingestion Setup

#### 2.1 IoT Hub Configuration
```json
{
  "deviceConnectionString": "HostName=fleet-iot-hub.azure-devices.net;DeviceId={device_id};SharedAccessKey={key}",
  "telemetryInterval": 30,
  "batchSize": 100,
  "compression": true
}
```

#### 2.2 Data Routing
```bash
# Create Event Hub for telemetry data
az eventhubs namespace create --name fleet-telemetry --resource-group FleetManagementDT
az eventhubs eventhub create --name vehicle-telemetry --namespace-name fleet-telemetry --resource-group FleetManagementDT

# Create routing rule
az iot hub routing-endpoint create --hub-name fleet-iot-hub --endpoint-name telemetry-endpoint --endpoint-type eventhub --endpoint-resource-group FleetManagementDT --endpoint-subscription-id {subscription-id} --connection-string {eventhub-connection-string}
```

### Phase 3: AI/ML Model Deployment

#### 3.1 Azure Machine Learning Workspace
```bash
# Create ML workspace
az ml workspace create --name fleet-ml-workspace --resource-group FleetManagementDT

# Deploy predictive maintenance model
az ml model deploy --name predictive-maintenance --model predictive-maintenance:1 --compute-target aks-cluster --deployment-config deployment-config.yml
```

#### 3.2 Model Configuration
```yaml
# deployment-config.yml
name: predictive-maintenance-service
compute_type: AKS
compute_target: aks-cluster
auth_enabled: true
ssl_enabled: true
replica_count: 3
cpu_cores: 2
memory_gb: 4
scoring_timeout_ms: 60000
```

### Phase 4: Digital Twin Instance Creation

#### 4.1 Create Fleet Digital Twin
```bash
# Create fleet instance
az dt twin create --dt-name fleet-management-dt --twin-id "fleet-001" --model "dtmi:fleetmanagement:Fleet;1" --properties '{
  "fleetId": "fleet-001",
  "fleetName": "Main Fleet",
  "totalVehicles": 100,
  "operationalStatus": "operational"
}'
```

#### 4.2 Create Vehicle Instances
```bash
# Create vehicle instances
az dt twin create --dt-name fleet-management-dt --twin-id "vehicle-001" --model "dtmi:fleetmanagement:ElectricVehicle;1" --properties '{
  "vehicleId": "vehicle-001",
  "vin": "1HGBH41JXMN109186",
  "make": "Tesla",
  "model": "Model 3",
  "year": 2023,
  "powertrainType": "electric",
  "batteryCapacity": 75.0
}'

# Create relationship
az dt twin relationship create --dt-name fleet-management-dt --twin-id "fleet-001" --relationship-id "has-vehicle-001" --relationship "hasVehicles" --target "vehicle-001"
```

### Phase 5: Real-time Data Processing

#### 5.1 Azure Functions for Data Processing
```javascript
// function.js - Vehicle telemetry processor
module.exports = async function (context, eventHubMessages) {
    const digitalTwinsClient = new DigitalTwinsClient(
        process.env.AZURE_DIGITALTWINS_URL,
        new DefaultAzureCredential()
    );

    for (const message of eventHubMessages) {
        const { vehicleId, telemetry } = message;
        
        // Update vehicle twin with telemetry
        await digitalTwinsClient.updateDigitalTwin(vehicleId, [
            {
                op: "replace",
                path: "/location",
                value: telemetry.location
            },
            {
                op: "replace",
                path: "/engineDiagnostics",
                value: telemetry.engineDiagnostics
            }
        ]);

        // Trigger AI analysis
        await triggerAnomalyDetection(vehicleId, telemetry);
        await triggerPredictiveMaintenance(vehicleId, telemetry);
    }
};
```

#### 5.2 Stream Analytics Job
```sql
-- Anomaly detection query
SELECT
    vehicleId,
    System.Timestamp AS timestamp,
    engineTemperature,
    oilPressure,
    AnomalyDetection_SpikeAndDip(engineTemperature, 95, 120, 'spikesanddips')
        OVER(LIMIT DURATION(minute, 5)) AS anomaly
INTO
    [anomaly-output]
FROM
    [vehicle-telemetry]
WHERE
    AnomalyDetection_SpikeAndDip(engineTemperature, 95, 120, 'spikesanddips')
        OVER(LIMIT DURATION(minute, 5)) IS NOT NULL
```

### Phase 6: AI Model Integration

#### 6.1 Predictive Maintenance Integration
```python
# predictive_maintenance_service.py
import joblib
import numpy as np
from azure.digitaltwins.core import DigitalTwinsClient

class PredictiveMaintenanceService:
    def __init__(self):
        self.model = joblib.load('predictive_maintenance_model.pkl')
        self.dt_client = DigitalTwinsClient(endpoint, credential)
    
    async def predict_maintenance(self, vehicle_id, telemetry_data):
        # Prepare features
        features = self.prepare_features(telemetry_data)
        
        # Make prediction
        failure_probability = self.model.predict_proba(features)[0][1]
        days_to_failure = self.model.predict_days_to_failure(features)
        
        # Update digital twin
        await self.dt_client.update_digital_twin(
            vehicle_id,
            [
                {
                    "op": "replace",
                    "path": "/vehicleHealth/maintenanceScore",
                    "value": (1 - failure_probability) * 100
                }
            ]
        )
        
        return {
            "failure_probability": failure_probability,
            "days_to_failure": days_to_failure,
            "recommendation": self.get_recommendation(failure_probability)
        }
```

### Phase 7: Visualization and Monitoring

#### 7.1 Power BI Dashboard Setup
```json
{
  "dataSource": {
    "type": "AzureDigitalTwins",
    "endpoint": "https://fleet-management-dt.api.wcus.digitaltwins.azure.net",
    "authentication": "ServicePrincipal"
  },
  "queries": [
    {
      "name": "FleetOverview",
      "query": "SELECT * FROM digitaltwins WHERE IS_OF_MODEL('dtmi:fleetmanagement:Fleet;1')"
    },
    {
      "name": "VehicleHealth",
      "query": "SELECT vehicleId, vehicleHealth FROM digitaltwins WHERE IS_OF_MODEL('dtmi:fleetmanagement:Vehicle;1')"
    }
  ]
}
```

#### 7.2 Monitoring and Alerts
```yaml
# monitoring-config.yml
alerts:
  - name: "High Anomaly Score"
    condition: "anomalyScore > 80"
    action: "send_notification"
    recipients: ["<EMAIL>"]
  
  - name: "Maintenance Required"
    condition: "maintenanceScore < 30"
    action: "create_work_order"
    priority: "high"
  
  - name: "Charging Station Offline"
    condition: "operationalStatus = 'offline'"
    action: "dispatch_technician"
    sla: "2 hours"
```

## Validation and Testing

### 1. Model Validation
```bash
# Validate DTDL models
az dt model show --dt-name fleet-management-dt --model-id "dtmi:fleetmanagement:Fleet;1"

# Test relationships
az dt twin relationship list --dt-name fleet-management-dt --twin-id "fleet-001"
```

### 2. Data Flow Testing
```bash
# Send test telemetry
az iot device send-d2c-message --hub-name fleet-iot-hub --device-id vehicle-001 --data '{
  "location": {"latitude": 40.7128, "longitude": -74.0060},
  "engineDiagnostics": {"temperature": 85.5, "oilPressure": 45.2}
}'
```

### 3. AI Model Testing
```python
# Test predictive maintenance
response = requests.post(
    "https://predictive-maintenance-service.azurewebsites.net/predict",
    json={"vehicle_id": "vehicle-001", "telemetry": test_data}
)
print(response.json())
```

## Maintenance and Operations

### Regular Tasks
1. **Model Updates**: Retrain AI models monthly with new data
2. **Performance Monitoring**: Monitor digital twin query performance
3. **Data Quality**: Validate incoming telemetry data quality
4. **Security**: Rotate access keys and certificates quarterly
5. **Backup**: Regular backup of digital twin configurations

### Scaling Considerations
- **Horizontal Scaling**: Add more digital twin instances for larger fleets
- **Performance Optimization**: Implement caching for frequently accessed data
- **Cost Optimization**: Use appropriate pricing tiers based on usage patterns

## Troubleshooting

### Common Issues
1. **Model Upload Failures**: Check DTDL syntax and dependencies
2. **Telemetry Ingestion Issues**: Verify IoT Hub connectivity and routing
3. **AI Model Errors**: Check model endpoints and authentication
4. **Performance Issues**: Monitor resource utilization and optimize queries

### Support Resources
- Azure Digital Twins Documentation
- DTDL Language Reference
- Azure IoT Hub Troubleshooting Guide
- Azure Machine Learning Support

## Security Best Practices

1. **Authentication**: Use Azure AD for all service authentication
2. **Authorization**: Implement role-based access control (RBAC)
3. **Encryption**: Enable encryption in transit and at rest
4. **Network Security**: Use private endpoints and VNets
5. **Monitoring**: Enable audit logging and security monitoring

## Cost Optimization

1. **Resource Sizing**: Right-size compute resources based on actual usage
2. **Data Retention**: Implement appropriate data retention policies
3. **Monitoring**: Use Azure Cost Management for cost tracking
4. **Reserved Instances**: Consider reserved instances for predictable workloads

This deployment guide provides a comprehensive approach to implementing the fleet management digital twins solution with full AI capabilities.
