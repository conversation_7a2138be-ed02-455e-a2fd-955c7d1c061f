# Fleet Management Digital Twins Deployment Guide - AWS

## Overview

This guide provides step-by-step instructions for deploying the comprehensive fleet management digital twins solution with AI capabilities on AWS.

## Prerequisites

### Infrastructure Requirements
- **Cloud Platform**: AWS
- **Digital Twins Platform**: AWS IoT TwinMaker
- **IoT Platform**: AWS IoT Core
- **AI/ML Platform**: Amazon SageMaker
- **Analytics Platform**: Amazon Kinesis Analytics, Amazon Redshift
- **Visualization**: Amazon QuickSight, Grafana on AWS
- **Time Series Database**: Amazon Timestream

### Technical Prerequisites
- AWS CLI configured with appropriate permissions
- AWS CDK or CloudFormation for infrastructure as code
- Real-time data streaming with Amazon Kinesis
- Machine learning model deployment with SageMaker
- Time-series database for telemetry data
- Event processing with AWS Lambda

## AWS Deployment Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   IoT Devices   │───▶│   AWS IoT Core  │───▶│ AWS TwinMaker   │
│                 │    │                 │    │                 │
│ • Vehicles      │    │ • Device Mgmt   │    │ • Digital Twins │
│ • Charging      │    │ • Rules Engine  │    │ • 3D Scenes     │
│ • Sensors       │    │ • Security      │    │ • Relationships │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Amazon Kinesis │───▶│   AWS Lambda    │───▶│ Amazon S3       │
│                 │    │                 │    │                 │
│ • Data Streams  │    │ • Real-time     │    │ • Data Lake     │
│ • Analytics     │    │ • Processing    │    │ • Model Store   │
│ • Firehose      │    │ • ML Inference  │    │ • Backups       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Amazon SageMaker│───▶│ Amazon Timestream│───▶│ Amazon QuickSight│
│                 │    │                 │    │                 │
│ • ML Models     │    │ • Time Series   │    │ • Dashboards    │
│ • Training      │    │ • Real-time     │    │ • Analytics     │
│ • Inference     │    │ • Queries       │    │ • Alerts        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Step-by-Step Deployment

### Phase 1: AWS Infrastructure Setup

#### 1.1 AWS IoT TwinMaker Setup
```bash
# Set AWS region
export AWS_REGION=us-east-1

# Create S3 bucket for TwinMaker workspace
aws s3 mb s3://fleet-management-twinmaker-workspace --region $AWS_REGION

# Create IAM role for TwinMaker
aws iam create-role --role-name TwinMakerExecutionRole --assume-role-policy-document file://twinmaker-trust-policy.json

# Attach policies to the role
aws iam attach-role-policy --role-name TwinMakerExecutionRole --policy-arn arn:aws:iam::aws:policy/AWSIoTTwinMakerServiceRolePolicy

# Create TwinMaker workspace
aws iottwinmaker create-workspace \
    --workspace-id fleet-management-workspace \
    --description "Fleet Management Digital Twins Workspace" \
    --s3-location s3://fleet-management-twinmaker-workspace \
    --role arn:aws:iam::ACCOUNT-ID:role/TwinMakerExecutionRole
```

#### 1.2 AWS IoT Core Setup
```bash
# Create IoT Thing Type for vehicles
aws iot create-thing-type \
    --thing-type-name FleetVehicle \
    --thing-type-description "Fleet vehicle IoT thing type"

# Create IoT Thing Type for charging stations
aws iot create-thing-type \
    --thing-type-name ChargingStation \
    --thing-type-description "EV charging station IoT thing type"

# Create IoT policy for devices
aws iot create-policy \
    --policy-name FleetDevicePolicy \
    --policy-document file://iot-device-policy.json
```

#### 1.3 Create Component Types in TwinMaker
```bash
# Create Vehicle component type
aws iottwinmaker create-component-type \
    --workspace-id fleet-management-workspace \
    --component-type-id VehicleComponent \
    --description "Vehicle digital twin component" \
    --property-definitions file://vehicle-properties.json

# Create EV component type
aws iottwinmaker create-component-type \
    --workspace-id fleet-management-workspace \
    --component-type-id ElectricVehicleComponent \
    --description "Electric vehicle digital twin component" \
    --extends-from VehicleComponent \
    --property-definitions file://ev-properties.json

# Create Driver component type
aws iottwinmaker create-component-type \
    --workspace-id fleet-management-workspace \
    --component-type-id DriverComponent \
    --description "Driver digital twin component" \
    --property-definitions file://driver-properties.json
```

### Phase 2: Data Ingestion and Streaming Setup

#### 2.1 Deploy Infrastructure with CloudFormation
```bash
# Deploy the main infrastructure stack
aws cloudformation create-stack \
    --stack-name fleet-management-infrastructure \
    --template-body file://aws-config/fleet-management-infrastructure.yaml \
    --capabilities CAPABILITY_IAM \
    --parameters ParameterKey=WorkspaceName,ParameterValue=fleet-management-workspace

# Wait for stack creation to complete
aws cloudformation wait stack-create-complete \
    --stack-name fleet-management-infrastructure
```

#### 2.2 Create IoT Things and Certificates
```bash
# Create IoT things for vehicles
for i in {1..10}; do
    # Create thing
    aws iot create-thing \
        --thing-name "vehicle-$(printf "%03d" $i)" \
        --thing-type-name FleetVehicle

    # Create certificate
    aws iot create-keys-and-certificate \
        --set-as-active \
        --certificate-pem-outfile "vehicle-$(printf "%03d" $i)-certificate.pem" \
        --public-key-outfile "vehicle-$(printf "%03d" $i)-public.pem" \
        --private-key-outfile "vehicle-$(printf "%03d" $i)-private.pem"

    # Attach policy to certificate
    CERT_ARN=$(aws iot list-certificates --query 'certificates[0].certificateArn' --output text)
    aws iot attach-policy \
        --policy-name FleetDevicePolicy \
        --target $CERT_ARN

    # Attach certificate to thing
    aws iot attach-thing-principal \
        --thing-name "vehicle-$(printf "%03d" $i)" \
        --principal $CERT_ARN
done
```

#### 2.3 Configure Data Streaming
```bash
# Create additional Kinesis streams for different data types
aws kinesis create-stream \
    --stream-name fleet-driver-behavior \
    --shard-count 2

aws kinesis create-stream \
    --stream-name fleet-charging-data \
    --shard-count 2

# Create IoT rules for different data types
aws iot create-topic-rule \
    --rule-name FleetDriverBehaviorRule \
    --topic-rule-payload '{
        "sql": "SELECT * FROM \"fleet/drivers/+/behavior\"",
        "actions": [{
            "kinesis": {
                "streamName": "fleet-driver-behavior",
                "partitionKey": "${driverId}",
                "roleArn": "arn:aws:iam::ACCOUNT-ID:role/IoTRuleRole"
            }
        }]
    }'

aws iot create-topic-rule \
    --rule-name FleetChargingDataRule \
    --topic-rule-payload '{
        "sql": "SELECT * FROM \"fleet/charging-stations/+/status\"",
        "actions": [{
            "kinesis": {
                "streamName": "fleet-charging-data",
                "partitionKey": "${stationId}",
                "roleArn": "arn:aws:iam::ACCOUNT-ID:role/IoTRuleRole"
            }
        }]
    }'
```

### Phase 3: AI/ML Model Deployment with SageMaker

#### 3.1 Create SageMaker Domain and User Profile
```bash
# Create SageMaker execution role
aws iam create-role \
    --role-name SageMakerExecutionRole \
    --assume-role-policy-document file://sagemaker-trust-policy.json

aws iam attach-role-policy \
    --role-name SageMakerExecutionRole \
    --policy-arn arn:aws:iam::aws:policy/AmazonSageMakerFullAccess

# Create SageMaker domain
aws sagemaker create-domain \
    --domain-name fleet-management-domain \
    --auth-mode IAM \
    --default-user-settings '{
        "ExecutionRole": "arn:aws:iam::ACCOUNT-ID:role/SageMakerExecutionRole"
    }' \
    --subnet-ids subnet-12345 subnet-67890 \
    --vpc-id vpc-12345
```

#### 3.2 Train and Deploy Predictive Maintenance Model
```bash
# Upload training data to S3
aws s3 cp training-data/ s3://fleet-data-lake-ACCOUNT-ID/training-data/ --recursive

# Create training job
aws sagemaker create-training-job \
    --training-job-name predictive-maintenance-training-$(date +%Y%m%d%H%M%S) \
    --algorithm-specification '{
        "TrainingImage": "************.dkr.ecr.us-east-1.amazonaws.com/sklearn-inference:0.23-1-cpu-py3",
        "TrainingInputMode": "File"
    }' \
    --role-arn arn:aws:iam::ACCOUNT-ID:role/SageMakerExecutionRole \
    --input-data-config '[{
        "ChannelName": "train",
        "DataSource": {
            "S3DataSource": {
                "S3DataType": "S3Prefix",
                "S3Uri": "s3://fleet-data-lake-ACCOUNT-ID/training-data/",
                "S3DataDistributionType": "FullyReplicated"
            }
        }
    }]' \
    --output-data-config '{
        "S3OutputPath": "s3://fleet-data-lake-ACCOUNT-ID/model-artifacts/"
    }' \
    --resource-config '{
        "InstanceType": "ml.m5.large",
        "InstanceCount": 1,
        "VolumeSizeInGB": 30
    }' \
    --stopping-condition '{
        "MaxRuntimeInSeconds": 3600
    }'

# Create model
aws sagemaker create-model \
    --model-name predictive-maintenance-model \
    --primary-container '{
        "Image": "************.dkr.ecr.us-east-1.amazonaws.com/sklearn-inference:0.23-1-cpu-py3",
        "ModelDataUrl": "s3://fleet-data-lake-ACCOUNT-ID/model-artifacts/MODEL-ARTIFACTS.tar.gz"
    }' \
    --execution-role-arn arn:aws:iam::ACCOUNT-ID:role/SageMakerExecutionRole

# Create endpoint configuration
aws sagemaker create-endpoint-config \
    --endpoint-config-name predictive-maintenance-config \
    --production-variants '[{
        "VariantName": "primary",
        "ModelName": "predictive-maintenance-model",
        "InitialInstanceCount": 1,
        "InstanceType": "ml.t2.medium",
        "InitialVariantWeight": 1.0
    }]'

# Create endpoint
aws sagemaker create-endpoint \
    --endpoint-name predictive-maintenance-endpoint \
    --endpoint-config-name predictive-maintenance-config
```

#### 3.3 Deploy Lambda Functions for AI Processing
```bash
# Package and deploy predictive maintenance Lambda
cd aws-lambda/predictive-maintenance
zip -r predictive-maintenance.zip .
aws s3 cp predictive-maintenance.zip s3://fleet-data-lake-ACCOUNT-ID/lambda-functions/

aws lambda create-function \
    --function-name fleet-predictive-maintenance \
    --runtime python3.9 \
    --role arn:aws:iam::ACCOUNT-ID:role/TelemetryProcessorRole \
    --handler lambda_function.lambda_handler \
    --code S3Bucket=fleet-data-lake-ACCOUNT-ID,S3Key=lambda-functions/predictive-maintenance.zip \
    --timeout 300 \
    --memory-size 512 \
    --environment Variables='{
        "SAGEMAKER_ENDPOINT": "predictive-maintenance-endpoint",
        "TIMESTREAM_DATABASE": "FleetManagementDB",
        "TIMESTREAM_TABLE": "VehicleTelemetry",
        "TWINMAKER_WORKSPACE": "fleet-management-workspace"
    }'

# Package and deploy anomaly detection Lambda
cd ../anomaly-detection
zip -r anomaly-detection.zip .
aws s3 cp anomaly-detection.zip s3://fleet-data-lake-ACCOUNT-ID/lambda-functions/

aws lambda create-function \
    --function-name fleet-anomaly-detection \
    --runtime python3.9 \
    --role arn:aws:iam::ACCOUNT-ID:role/TelemetryProcessorRole \
    --handler lambda_function.lambda_handler \
    --code S3Bucket=fleet-data-lake-ACCOUNT-ID,S3Key=lambda-functions/anomaly-detection.zip \
    --timeout 300 \
    --memory-size 512 \
    --environment Variables='{
        "TIMESTREAM_DATABASE": "FleetManagementDB",
        "TIMESTREAM_TABLE": "VehicleTelemetry",
        "TWINMAKER_WORKSPACE": "fleet-management-workspace"
    }'
```

### Phase 4: Digital Twin Instance Creation

#### 4.1 Create Fleet Digital Twin
```bash
# Create fleet instance
az dt twin create --dt-name fleet-management-dt --twin-id "fleet-001" --model "dtmi:fleetmanagement:Fleet;1" --properties '{
  "fleetId": "fleet-001",
  "fleetName": "Main Fleet",
  "totalVehicles": 100,
  "operationalStatus": "operational"
}'
```

#### 4.2 Create Vehicle Instances
```bash
# Create vehicle instances
az dt twin create --dt-name fleet-management-dt --twin-id "vehicle-001" --model "dtmi:fleetmanagement:ElectricVehicle;1" --properties '{
  "vehicleId": "vehicle-001",
  "vin": "1HGBH41JXMN109186",
  "make": "Tesla",
  "model": "Model 3",
  "year": 2023,
  "powertrainType": "electric",
  "batteryCapacity": 75.0
}'

# Create relationship
az dt twin relationship create --dt-name fleet-management-dt --twin-id "fleet-001" --relationship-id "has-vehicle-001" --relationship "hasVehicles" --target "vehicle-001"
```

### Phase 5: Real-time Data Processing

#### 5.1 Azure Functions for Data Processing
```javascript
// function.js - Vehicle telemetry processor
module.exports = async function (context, eventHubMessages) {
    const digitalTwinsClient = new DigitalTwinsClient(
        process.env.AZURE_DIGITALTWINS_URL,
        new DefaultAzureCredential()
    );

    for (const message of eventHubMessages) {
        const { vehicleId, telemetry } = message;

        // Update vehicle twin with telemetry
        await digitalTwinsClient.updateDigitalTwin(vehicleId, [
            {
                op: "replace",
                path: "/location",
                value: telemetry.location
            },
            {
                op: "replace",
                path: "/engineDiagnostics",
                value: telemetry.engineDiagnostics
            }
        ]);

        // Trigger AI analysis
        await triggerAnomalyDetection(vehicleId, telemetry);
        await triggerPredictiveMaintenance(vehicleId, telemetry);
    }
};
```

#### 5.2 Stream Analytics Job
```sql
-- Anomaly detection query
SELECT
    vehicleId,
    System.Timestamp AS timestamp,
    engineTemperature,
    oilPressure,
    AnomalyDetection_SpikeAndDip(engineTemperature, 95, 120, 'spikesanddips')
        OVER(LIMIT DURATION(minute, 5)) AS anomaly
INTO
    [anomaly-output]
FROM
    [vehicle-telemetry]
WHERE
    AnomalyDetection_SpikeAndDip(engineTemperature, 95, 120, 'spikesanddips')
        OVER(LIMIT DURATION(minute, 5)) IS NOT NULL
```

### Phase 6: AI Model Integration

#### 6.1 Predictive Maintenance Integration
```python
# predictive_maintenance_service.py
import joblib
import numpy as np
from azure.digitaltwins.core import DigitalTwinsClient

class PredictiveMaintenanceService:
    def __init__(self):
        self.model = joblib.load('predictive_maintenance_model.pkl')
        self.dt_client = DigitalTwinsClient(endpoint, credential)

    async def predict_maintenance(self, vehicle_id, telemetry_data):
        # Prepare features
        features = self.prepare_features(telemetry_data)

        # Make prediction
        failure_probability = self.model.predict_proba(features)[0][1]
        days_to_failure = self.model.predict_days_to_failure(features)

        # Update digital twin
        await self.dt_client.update_digital_twin(
            vehicle_id,
            [
                {
                    "op": "replace",
                    "path": "/vehicleHealth/maintenanceScore",
                    "value": (1 - failure_probability) * 100
                }
            ]
        )

        return {
            "failure_probability": failure_probability,
            "days_to_failure": days_to_failure,
            "recommendation": self.get_recommendation(failure_probability)
        }
```

### Phase 7: Visualization and Monitoring

#### 7.1 Power BI Dashboard Setup
```json
{
  "dataSource": {
    "type": "AzureDigitalTwins",
    "endpoint": "https://fleet-management-dt.api.wcus.digitaltwins.azure.net",
    "authentication": "ServicePrincipal"
  },
  "queries": [
    {
      "name": "FleetOverview",
      "query": "SELECT * FROM digitaltwins WHERE IS_OF_MODEL('dtmi:fleetmanagement:Fleet;1')"
    },
    {
      "name": "VehicleHealth",
      "query": "SELECT vehicleId, vehicleHealth FROM digitaltwins WHERE IS_OF_MODEL('dtmi:fleetmanagement:Vehicle;1')"
    }
  ]
}
```

#### 7.2 Monitoring and Alerts
```yaml
# monitoring-config.yml
alerts:
  - name: "High Anomaly Score"
    condition: "anomalyScore > 80"
    action: "send_notification"
    recipients: ["<EMAIL>"]

  - name: "Maintenance Required"
    condition: "maintenanceScore < 30"
    action: "create_work_order"
    priority: "high"

  - name: "Charging Station Offline"
    condition: "operationalStatus = 'offline'"
    action: "dispatch_technician"
    sla: "2 hours"
```

## Validation and Testing

### 1. Model Validation
```bash
# Validate DTDL models
az dt model show --dt-name fleet-management-dt --model-id "dtmi:fleetmanagement:Fleet;1"

# Test relationships
az dt twin relationship list --dt-name fleet-management-dt --twin-id "fleet-001"
```

### 2. Data Flow Testing
```bash
# Send test telemetry
az iot device send-d2c-message --hub-name fleet-iot-hub --device-id vehicle-001 --data '{
  "location": {"latitude": 40.7128, "longitude": -74.0060},
  "engineDiagnostics": {"temperature": 85.5, "oilPressure": 45.2}
}'
```

### 3. AI Model Testing
```python
# Test predictive maintenance
response = requests.post(
    "https://predictive-maintenance-service.azurewebsites.net/predict",
    json={"vehicle_id": "vehicle-001", "telemetry": test_data}
)
print(response.json())
```

## Maintenance and Operations

### Regular Tasks
1. **Model Updates**: Retrain AI models monthly with new data
2. **Performance Monitoring**: Monitor digital twin query performance
3. **Data Quality**: Validate incoming telemetry data quality
4. **Security**: Rotate access keys and certificates quarterly
5. **Backup**: Regular backup of digital twin configurations

### Scaling Considerations
- **Horizontal Scaling**: Add more digital twin instances for larger fleets
- **Performance Optimization**: Implement caching for frequently accessed data
- **Cost Optimization**: Use appropriate pricing tiers based on usage patterns

## Troubleshooting

### Common Issues
1. **Model Upload Failures**: Check DTDL syntax and dependencies
2. **Telemetry Ingestion Issues**: Verify IoT Hub connectivity and routing
3. **AI Model Errors**: Check model endpoints and authentication
4. **Performance Issues**: Monitor resource utilization and optimize queries

### Support Resources
- Azure Digital Twins Documentation
- DTDL Language Reference
- Azure IoT Hub Troubleshooting Guide
- Azure Machine Learning Support

## Security Best Practices

1. **Authentication**: Use Azure AD for all service authentication
2. **Authorization**: Implement role-based access control (RBAC)
3. **Encryption**: Enable encryption in transit and at rest
4. **Network Security**: Use private endpoints and VNets
5. **Monitoring**: Enable audit logging and security monitoring

## Cost Optimization

1. **Resource Sizing**: Right-size compute resources based on actual usage
2. **Data Retention**: Implement appropriate data retention policies
3. **Monitoring**: Use Azure Cost Management for cost tracking
4. **Reserved Instances**: Consider reserved instances for predictable workloads

This deployment guide provides a comprehensive approach to implementing the fleet management digital twins solution with full AI capabilities.
