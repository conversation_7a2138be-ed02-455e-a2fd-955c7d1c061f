# Fleet Management Digital Twins with AWS IoT SiteWise - Deployment Guide

## Overview

This guide provides step-by-step instructions for deploying the Fleet Management Digital Twins solution using AWS IoT SiteWise for industrial IoT data collection, processing, and visualization.

## Architecture Overview

The solution leverages AWS IoT SiteWise as the core platform for:
- **Asset Modeling**: Define digital twin models for vehicles, charging stations, and fleet
- **Data Collection**: Collect telemetry data from IoT devices
- **Real-time Processing**: Process and analyze data at the edge and cloud
- **Visualization**: Create dashboards and monitor fleet performance
- **AI/ML Integration**: Integrate with SageMaker for predictive analytics

### Key Components

1. **IoT SiteWise Asset Models**
   - Fleet Model (root level)
   - Vehicle Model (ICE vehicles)
   - Electric Vehicle Model (extends Vehicle)
   - Charging Station Model

2. **Data Flow**
   - IoT devices → AWS IoT Core → IoT SiteWise
   - SiteWise → Lambda functions → AI/ML processing
   - SiteWise → Portal/Dashboards → Visualization

3. **Edge Computing**
   - AWS Greengrass for local processing
   - SiteWise Gateway for edge data collection
   - Local analytics and filtering

## Prerequisites

### AWS Account Setup
- AWS CLI configured with appropriate permissions
- Terraform >= 1.0 installed
- Python 3.8+ installed
- Docker installed (for edge deployment)

### Required AWS Services
- AWS IoT Core
- AWS IoT SiteWise
- AWS IoT TwinMaker
- AWS Lambda
- Amazon SageMaker
- Amazon S3
- AWS Greengrass (for edge deployment)

### IAM Permissions
The deployment requires permissions for:
- IoT SiteWise (full access)
- IoT Core (full access)
- Lambda (create/update functions)
- SageMaker (model deployment)
- S3 (bucket management)
- IAM (role creation)

## Deployment Steps

### Step 1: Infrastructure Deployment

#### Option A: Automated Terraform Deployment
```bash
# Clone the repository
git clone <repository-url>
cd fleet-management-digital-twins

# Make deployment script executable
chmod +x deploy-terraform.sh

# Deploy the complete solution
./deploy-terraform.sh
```

#### Option B: Manual Terraform Deployment
```bash
# Navigate to terraform directory
cd terraform

# Copy and customize variables
cp terraform.tfvars.example terraform.tfvars
vim terraform.tfvars

# Initialize Terraform
terraform init
terraform workspace new production

# Plan and apply
terraform plan -out=tfplan
terraform apply tfplan
```

### Step 2: SiteWise Asset Model Configuration

The Terraform deployment creates the following asset models:

#### Fleet Model (Root)
- Properties: FleetId, FleetName, TotalVehicles
- Metrics: FleetUtilization, AverageMaintenanceScore
- Hierarchies: Vehicles, Charging Stations

#### Vehicle Model
- Attributes: VehicleId, Make, Model, Year
- Measurements: EngineTemperature, OilPressure, RPM, Speed, FuelLevel
- Metrics: FuelEfficiency, MaintenanceScore

#### Electric Vehicle Model (extends Vehicle)
- Additional Measurements: StateOfCharge, BatteryTemperature, ChargingPower
- Additional Metrics: EnergyEfficiency, BatteryHealthScore

#### Charging Station Model
- Measurements: CurrentPower, AvailableConnectors, EnergyDelivered
- Metrics: UtilizationRate, PowerEfficiency, DailyEnergyDelivered

### Step 3: Create Asset Instances

After deployment, create actual asset instances:

```bash
# Get asset model IDs from Terraform outputs
FLEET_MODEL_ID=$(terraform output -raw sitewise_fleet_model_id)
VEHICLE_MODEL_ID=$(terraform output -raw sitewise_vehicle_model_id)
EV_MODEL_ID=$(terraform output -raw sitewise_ev_model_id)
CHARGING_MODEL_ID=$(terraform output -raw sitewise_charging_station_model_id)

# Create fleet asset
aws iotsitewise create-asset \
    --asset-name "MainFleet" \
    --asset-model-id $FLEET_MODEL_ID \
    --asset-description "Main fleet digital twin"

# Create vehicle assets
for i in {1..5}; do
    VEHICLE_ID=$(printf "vehicle-%03d" $i)
    aws iotsitewise create-asset \
        --asset-name $VEHICLE_ID \
        --asset-model-id $VEHICLE_MODEL_ID \
        --asset-description "Fleet vehicle $VEHICLE_ID"
done

# Create EV assets
for i in {6..10}; do
    VEHICLE_ID=$(printf "vehicle-%03d" $i)
    aws iotsitewise create-asset \
        --asset-name $VEHICLE_ID \
        --asset-model-id $EV_MODEL_ID \
        --asset-description "Electric vehicle $VEHICLE_ID"
done

# Create charging station assets
for i in {1..3}; do
    STATION_ID=$(printf "station-%03d" $i)
    aws iotsitewise create-asset \
        --asset-name $STATION_ID \
        --asset-model-id $CHARGING_MODEL_ID \
        --asset-description "Charging station $STATION_ID"
done
```

### Step 4: Configure IoT Device Connectivity

#### Create IoT Things and Certificates
```bash
# Create IoT things for vehicles
for i in {1..10}; do
    VEHICLE_ID=$(printf "vehicle-%03d" $i)
    
    # Create IoT thing
    aws iot create-thing \
        --thing-name $VEHICLE_ID \
        --thing-type-name FleetVehicle
    
    # Create certificate
    CERT_OUTPUT=$(aws iot create-keys-and-certificate --set-as-active)
    CERT_ARN=$(echo $CERT_OUTPUT | jq -r '.certificateArn')
    
    # Attach policy and thing
    aws iot attach-policy --policy-name FleetDevicePolicy --target $CERT_ARN
    aws iot attach-thing-principal --thing-name $VEHICLE_ID --principal $CERT_ARN
done
```

#### Configure Data Routing
The deployment automatically creates IoT topic rules that route data to SiteWise:
- Vehicle telemetry: `fleet/vehicles/+/telemetry` → SiteWise
- Battery data: `fleet/vehicles/+/battery` → SiteWise
- Charging station data: `fleet/charging-stations/+/status` → SiteWise

### Step 5: Edge Gateway Setup (Optional)

For edge processing capabilities:

#### Deploy Greengrass Core
```bash
# Download Greengrass Core software
wget https://d2s8p88vqu9w66.cloudfront.net/releases/greengrass-nucleus-latest.zip
unzip greengrass-nucleus-latest.zip

# Install Greengrass
sudo java -Droot="/greengrass/v2" -Dlog.store=FILE \
    -jar ./GreengrassInstaller/lib/Greengrass.jar \
    --aws-region us-east-1 \
    --thing-name fleet-gateway-thing \
    --thing-group-name fleet-gateway-group \
    --component-default-user ggc_user:ggc_group \
    --provision true \
    --setup-system-service true
```

#### Deploy SiteWise Connector
```bash
# Deploy SiteWise connector component
aws greengrassv2 create-deployment \
    --target-arn "arn:aws:iot:us-east-1:ACCOUNT:thinggroup/fleet-gateway-group" \
    --components '{
        "aws.iot.SiteWiseEdgeCollectorOpcua": {
            "componentVersion": "1.0.0"
        },
        "aws.iot.SiteWiseEdgeProcessor": {
            "componentVersion": "1.0.0"
        }
    }'
```

### Step 6: Configure SiteWise Portal and Dashboards

#### Access SiteWise Portal
```bash
# Get portal URL from Terraform output
PORTAL_URL=$(terraform output -raw sitewise_portal_url)
echo "SiteWise Portal URL: $PORTAL_URL"
```

#### Create Custom Dashboards
1. Log into the SiteWise Portal
2. Navigate to the Fleet Management Project
3. Create dashboards for:
   - Fleet Overview (KPIs, utilization)
   - Vehicle Health Monitoring
   - Predictive Maintenance Alerts
   - EV Battery Management
   - Charging Station Performance

### Step 7: AI/ML Integration

#### Deploy SageMaker Models
```bash
# Train predictive maintenance model
aws sagemaker create-training-job \
    --training-job-name fleet-predictive-maintenance \
    --algorithm-specification TrainingImage=763104351884.dkr.ecr.us-east-1.amazonaws.com/sklearn-inference:0.23-1-cpu-py3 \
    --role-arn $(terraform output -raw sagemaker_execution_role_arn) \
    --input-data-config file://training-config.json \
    --output-data-config S3OutputPath=s3://$(terraform output -raw data_lake_bucket_name)/models/

# Deploy model endpoint
aws sagemaker create-endpoint-config \
    --endpoint-config-name predictive-maintenance-config \
    --production-variants file://endpoint-config.json

aws sagemaker create-endpoint \
    --endpoint-name predictive-maintenance-endpoint \
    --endpoint-config-name predictive-maintenance-config
```

### Step 8: Testing and Validation

#### Run Vehicle Simulator
```bash
# Test data flow with vehicle simulator
cd aws-testing
python3 vehicle_simulator.py --vehicles 5 --duration 60
```

#### Verify Data Flow
```bash
# Check SiteWise asset property values
aws iotsitewise get-asset-property-value \
    --asset-id ASSET_ID \
    --property-id PROPERTY_ID

# Monitor Lambda function logs
aws logs tail /aws/lambda/fleet-sitewise-processor --follow
```

#### Validate Dashboards
1. Access SiteWise Portal
2. Verify real-time data visualization
3. Check metric calculations
4. Test alert notifications

## Monitoring and Operations

### CloudWatch Monitoring
- Lambda function metrics and logs
- IoT Core message metrics
- SiteWise data ingestion metrics
- SageMaker endpoint performance

### Alerting
- SNS topics for maintenance alerts
- CloudWatch alarms for system health
- SiteWise alarms for asset monitoring

### Data Retention
- SiteWise: Configurable retention (default 365 days)
- S3 Data Lake: Lifecycle policies for cost optimization
- CloudWatch Logs: 14-day retention

## Troubleshooting

### Common Issues

#### Data Not Appearing in SiteWise
1. Check IoT topic rule execution
2. Verify asset model property mappings
3. Check Lambda function logs
4. Validate device certificates and policies

#### Dashboard Not Updating
1. Verify asset associations
2. Check property aliases
3. Validate time ranges
4. Check portal permissions

#### Edge Gateway Issues
1. Check Greengrass connectivity
2. Verify component deployment status
3. Check local logs on edge device
4. Validate network connectivity

### Useful Commands
```bash
# List SiteWise assets
aws iotsitewise list-assets --asset-model-id MODEL_ID

# Get asset property history
aws iotsitewise get-asset-property-value-history \
    --asset-id ASSET_ID \
    --property-id PROPERTY_ID \
    --start-date 2024-01-01T00:00:00Z \
    --end-date 2024-01-02T00:00:00Z

# Check IoT thing connectivity
aws iot describe-thing --thing-name THING_NAME

# Monitor Lambda function
aws logs tail /aws/lambda/FUNCTION_NAME --follow
```

## Cost Optimization

### SiteWise Pricing
- Asset modeling: Free
- Data ingestion: $0.25 per 1,000 property values
- Data processing: $0.50 per 1,000 property values processed
- Data storage: $0.045 per GB-month

### Optimization Strategies
- Use edge processing to reduce cloud data transfer
- Implement data filtering at the edge
- Configure appropriate data retention policies
- Use computed metrics instead of raw data where possible

## Security Best Practices

### Device Security
- Use X.509 certificates for device authentication
- Implement least-privilege IoT policies
- Regular certificate rotation
- Monitor device connectivity patterns

### Data Security
- Enable encryption in transit and at rest
- Use VPC endpoints for private connectivity
- Implement proper IAM roles and policies
- Regular security audits

### Network Security
- Use private subnets for sensitive resources
- Implement security groups and NACLs
- Monitor network traffic
- Use AWS WAF for API protection

## Next Steps

1. **Scale Deployment**: Add more vehicles and charging stations
2. **Advanced Analytics**: Implement more sophisticated ML models
3. **Integration**: Connect with existing fleet management systems
4. **Optimization**: Fine-tune performance and costs
5. **Automation**: Implement CI/CD for model updates
