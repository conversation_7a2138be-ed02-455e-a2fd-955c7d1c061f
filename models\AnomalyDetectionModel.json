{"@context": "dtmi:dtdl:context;3", "@id": "dtmi:fleetmanagement:AnomalyDetectionModel;1", "@type": "Interface", "displayName": "Anomaly Detection AI Model", "description": "AI-powered anomaly detection model for real-time fleet monitoring and early warning systems", "contents": [{"@type": "Property", "name": "modelId", "displayName": "Model ID", "description": "Unique identifier for the anomaly detection model", "schema": "string"}, {"@type": "Property", "name": "modelName", "displayName": "Model Name", "description": "Name of the anomaly detection model", "schema": "string"}, {"@type": "Property", "name": "modelVersion", "displayName": "Model Version", "description": "Version of the AI model", "schema": "string"}, {"@type": "Property", "name": "detectionAlgorithm", "displayName": "Detection Algorithm", "description": "Type of anomaly detection algorithm", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "IsolationForest", "enumValue": "isolation_forest"}, {"name": "OneClassSVM", "enumValue": "one_class_svm"}, {"name": "LSTM_Autoencoder", "enumValue": "lstm_autoencoder"}, {"name": "StatisticalOutlier", "enumValue": "statistical_outlier"}, {"name": "EnsembleMethod", "enumValue": "ensemble"}, {"name": "DeepLearning", "enumValue": "deep_learning"}]}}, {"@type": "Property", "name": "sensitivityLevel", "displayName": "Sensitivity Level", "description": "Anomaly detection sensitivity setting", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Low", "displayName": "Low Sensitivity", "enumValue": "low"}, {"name": "Medium", "displayName": "Medium Sensitivity", "enumValue": "medium"}, {"name": "High", "displayName": "High Sensitivity", "enumValue": "high"}, {"name": "Adaptive", "displayName": "Adaptive Sensitivity", "enumValue": "adaptive"}]}}, {"@type": "Property", "name": "monitoredParameters", "displayName": "Monitored Parameters", "description": "List of vehicle parameters being monitored for anomalies", "schema": {"@type": "Array", "elementSchema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "EngineTemperature", "enumValue": "engine_temperature"}, {"name": "OilPressure", "enumValue": "oil_pressure"}, {"name": "FuelConsumption", "enumValue": "fuel_consumption"}, {"name": "BatteryVoltage", "enumValue": "battery_voltage"}, {"name": "TirePressure", "enumValue": "tire_pressure"}, {"name": "VibrationLevel", "enumValue": "vibration_level"}, {"name": "Driving<PERSON><PERSON><PERSON>or", "enumValue": "driving_behavior"}, {"name": "EmissionLevels", "enumValue": "emission_levels"}]}}}, {"@type": "Telemetry", "name": "anomalyDetection", "displayName": "Real-time Anomaly Detection", "description": "Real-time anomaly detection results and alerts", "schema": {"@type": "Object", "fields": [{"name": "vehicleId", "displayName": "Vehicle ID", "schema": "string"}, {"name": "anomalyScore", "displayName": "Anomaly Score (0-100)", "schema": "double"}, {"name": "anomalyType", "displayName": "Anomaly Type", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "PerformanceAnomaly", "enumValue": "performance"}, {"name": "BehavioralAnomaly", "enumValue": "behavioral"}, {"name": "MechanicalAnomaly", "enumValue": "mechanical"}, {"name": "ElectricalAnomaly", "enumValue": "electrical"}, {"name": "OperationalAnomaly", "enumValue": "operational"}, {"name": "SafetyAnomaly", "enumValue": "safety"}]}}, {"name": "severity", "displayName": "Anomaly Severity", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Low", "enumValue": "low"}, {"name": "Medium", "enumValue": "medium"}, {"name": "High", "enumValue": "high"}, {"name": "Critical", "enumValue": "critical"}]}}, {"name": "affectedParameters", "displayName": "Affected Parameters", "schema": {"@type": "Array", "elementSchema": {"@type": "Object", "fields": [{"name": "parameterName", "schema": "string"}, {"name": "expectedValue", "schema": "double"}, {"name": "actualValue", "schema": "double"}, {"name": "deviation", "schema": "double"}]}}}, {"name": "detectionTimestamp", "displayName": "Detection Timestamp", "schema": "dateTime"}, {"name": "confidence", "displayName": "Detection Confidence (%)", "schema": "double"}, {"name": "rootCauseAnalysis", "displayName": "Potential Root Causes", "schema": {"@type": "Array", "elementSchema": {"@type": "Object", "fields": [{"name": "cause", "schema": "string"}, {"name": "probability", "schema": "double"}, {"name": "category", "schema": "string"}]}}}]}}, {"@type": "Telemetry", "name": "modelPerformance", "displayName": "Model Performance Metrics", "description": "Performance metrics for the anomaly detection model", "schema": {"@type": "Object", "fields": [{"name": "truePositiveRate", "displayName": "True Positive Rate", "schema": "double"}, {"name": "falsePositiveRate", "displayName": "False Positive Rate", "schema": "double"}, {"name": "trueNegativeRate", "displayName": "True Negative Rate", "schema": "double"}, {"name": "falseNegativeRate", "displayName": "False Negative Rate", "schema": "double"}, {"name": "precision", "displayName": "Precision", "schema": "double"}, {"name": "recall", "displayName": "Recall", "schema": "double"}, {"name": "f1Score", "displayName": "F1 Score", "schema": "double"}, {"name": "detectionLatency", "displayName": "Average Detection Latency (ms)", "schema": "double"}, {"name": "processingThroughput", "displayName": "Processing Throughput (events/sec)", "schema": "double"}]}}, {"@type": "Telemetry", "name": "alertStatistics", "displayName": "<PERSON>ert <PERSON>", "description": "Statistics about generated alerts and their outcomes", "schema": {"@type": "Object", "fields": [{"name": "totalAlertsGenerated", "displayName": "Total Alerts Generated", "schema": "integer"}, {"name": "alertsByType", "displayName": "Alerts by Type", "schema": {"@type": "Object", "fields": [{"name": "performance", "schema": "integer"}, {"name": "behavioral", "schema": "integer"}, {"name": "mechanical", "schema": "integer"}, {"name": "electrical", "schema": "integer"}, {"name": "operational", "schema": "integer"}, {"name": "safety", "schema": "integer"}]}}, {"name": "alertsBySeverity", "displayName": "Alerts by Severity", "schema": {"@type": "Object", "fields": [{"name": "low", "schema": "integer"}, {"name": "medium", "schema": "integer"}, {"name": "high", "schema": "integer"}, {"name": "critical", "schema": "integer"}]}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "displayName": "Resolved <PERSON><PERSON><PERSON>", "schema": "integer"}, {"name": "falseAlerts", "displayName": "<PERSON>als<PERSON>", "schema": "integer"}, {"name": "averageResolutionTime", "displayName": "Average Resolution Time (hours)", "schema": "double"}]}}, {"@type": "Command", "name": "detectAnomalies", "displayName": "Detect Anomalies", "description": "Run anomaly detection on specific vehicle or fleet data", "request": {"name": "detectionRequest", "schema": {"@type": "Object", "fields": [{"name": "vehicleId", "displayName": "Vehicle ID (optional for fleet-wide detection)", "schema": "string"}, {"name": "timeRange", "displayName": "Time Range for Analysis", "schema": {"@type": "Object", "fields": [{"name": "startTime", "schema": "dateTime"}, {"name": "endTime", "schema": "dateTime"}]}}, {"name": "parameters", "displayName": "Specific Parameters to Analyze", "schema": {"@type": "Array", "elementSchema": "string"}}, {"name": "sensitivityOverride", "displayName": "Override Sensitivity Level", "schema": "string"}]}}, "response": {"name": "detectionResult", "schema": {"@type": "Object", "fields": [{"name": "detectionId", "schema": "string"}, {"name": "anomaliesFound", "schema": "integer"}, {"name": "anomalies", "schema": {"@type": "Array", "elementSchema": {"@type": "Object", "fields": [{"name": "vehicleId", "schema": "string"}, {"name": "timestamp", "schema": "dateTime"}, {"name": "anomalyType", "schema": "string"}, {"name": "severity", "schema": "string"}, {"name": "score", "schema": "double"}, {"name": "description", "schema": "string"}]}}}, {"name": "recommendations", "schema": {"@type": "Array", "elementSchema": "string"}}]}}}, {"@type": "Command", "name": "updateThresholds", "displayName": "Update Detection Thresholds", "description": "Update anomaly detection thresholds and sensitivity settings", "request": {"name": "thresholdUpdate", "schema": {"@type": "Object", "fields": [{"name": "parameterThresholds", "schema": {"@type": "Array", "elementSchema": {"@type": "Object", "fields": [{"name": "parameter", "schema": "string"}, {"name": "lowerThreshold", "schema": "double"}, {"name": "upperThreshold", "schema": "double"}, {"name": "severity", "schema": "string"}]}}}, {"name": "globalSensitivity", "schema": "string"}, {"name": "adaptiveThresholds", "displayName": "Enable Adaptive Thresholds", "schema": "boolean"}]}}}, {"@type": "Command", "name": "generateAnomalyReport", "displayName": "Generate Anomaly Report", "description": "Generate comprehensive anomaly analysis report", "request": {"name": "reportRequest", "schema": {"@type": "Object", "fields": [{"name": "reportPeriod", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Daily", "enumValue": "daily"}, {"name": "Weekly", "enumValue": "weekly"}, {"name": "Monthly", "enumValue": "monthly"}, {"name": "Custom", "enumValue": "custom"}]}}, {"name": "includeRootCauseAnalysis", "schema": "boolean"}, {"name": "includeTrendAnalysis", "schema": "boolean"}, {"name": "vehicleFilter", "schema": {"@type": "Array", "elementSchema": "string"}}]}}, "response": {"name": "anomalyReport", "schema": {"@type": "Object", "fields": [{"name": "reportId", "schema": "string"}, {"name": "summary", "schema": {"@type": "Object", "fields": [{"name": "totalAnomalies", "schema": "integer"}, {"name": "criticalAnomalies", "schema": "integer"}, {"name": "mostAffectedVehicles", "schema": {"@type": "Array", "elementSchema": "string"}}, {"name": "commonPatterns", "schema": {"@type": "Array", "elementSchema": "string"}}]}}, {"name": "recommendations", "schema": {"@type": "Array", "elementSchema": "string"}}]}}}, {"@type": "Command", "name": "trainModel", "displayName": "Train Anomaly Detection Model", "description": "Train or retrain the anomaly detection model with new data", "request": {"name": "trainingRequest", "schema": {"@type": "Object", "fields": [{"name": "trainingDataPeriod", "schema": {"@type": "Object", "fields": [{"name": "startDate", "schema": "dateTime"}, {"name": "endDate", "schema": "dateTime"}]}}, {"name": "includeLabeled Data", "displayName": "Include Labeled Anomaly Data", "schema": "boolean"}, {"name": "algorithmSelection", "schema": "string"}, {"name": "hyperparameters", "schema": {"@type": "Object", "fields": [{"name": "contamination", "schema": "double"}, {"name": "nEstimators", "schema": "integer"}, {"name": "maxSamples", "schema": "double"}]}}]}}, "response": {"name": "trainingResult", "schema": {"@type": "Object", "fields": [{"name": "success", "schema": "boolean"}, {"name": "newModelVersion", "schema": "string"}, {"name": "performanceMetrics", "schema": {"@type": "Object", "fields": [{"name": "precision", "schema": "double"}, {"name": "recall", "schema": "double"}, {"name": "f1Score", "schema": "double"}]}}, {"name": "trainingDuration", "schema": "double"}]}}}, {"@type": "Relationship", "name": "monitoredVehicles", "displayName": "Monitored Vehicles", "target": "dtmi:fleetmanagement:Vehicle;1"}, {"@type": "Relationship", "name": "alertManagement", "displayName": "Alert Management System", "target": "dtmi:fleetmanagement:AlertManagement;1"}]}