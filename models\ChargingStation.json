{"@context": "dtmi:dtdl:context;3", "@id": "dtmi:fleetmanagement:ChargingStation;1", "@type": "Interface", "displayName": "EV Charging Station", "description": "Electric vehicle charging station with smart grid integration and AI optimization", "contents": [{"@type": "Property", "name": "stationId", "displayName": "Station ID", "description": "Unique identifier for the charging station", "schema": "string"}, {"@type": "Property", "name": "stationName", "displayName": "Station Name", "description": "Name of the charging station", "schema": "string"}, {"@type": "Property", "name": "location", "displayName": "Station Location", "description": "Geographic location of the charging station", "schema": {"@type": "Object", "fields": [{"name": "latitude", "displayName": "Latitude", "schema": "double"}, {"name": "longitude", "displayName": "Longitude", "schema": "double"}, {"name": "address", "displayName": "Street Address", "schema": "string"}, {"name": "city", "displayName": "City", "schema": "string"}, {"name": "postalCode", "displayName": "Postal Code", "schema": "string"}]}}, {"@type": "Property", "name": "stationType", "displayName": "Station Type", "description": "Type of charging station", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Level1", "displayName": "Level 1 (120V AC)", "enumValue": "level1"}, {"name": "Level2", "displayName": "Level 2 (240V AC)", "enumValue": "level2"}, {"name": "DCFastCharging", "displayName": "DC Fast Charging", "enumValue": "dc_fast"}, {"name": "Supercharger", "displayName": "Tesla Supercharger", "enumValue": "supercharger"}, {"name": "UltraFast", "displayName": "Ultra-Fast Charging (350kW+)", "enumValue": "ultra_fast"}]}}, {"@type": "Property", "name": "totalConnectors", "displayName": "Total Connectors", "description": "Total number of charging connectors", "schema": "integer"}, {"@type": "Property", "name": "maxPowerPerConnector", "displayName": "Max Power per Connector (kW)", "description": "Maximum power output per connector", "schema": "double"}, {"@type": "Property", "name": "supportedConnectorTypes", "displayName": "Supported Connector Types", "description": "Types of connectors available", "schema": {"@type": "Array", "elementSchema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "CCS", "enumValue": "ccs"}, {"name": "CHAdeMO", "enumValue": "chademo"}, {"name": "Type2", "enumValue": "type2"}, {"name": "Tesla", "enumValue": "tesla"}]}}}, {"@type": "Telemetry", "name": "stationStatus", "displayName": "Station Status", "description": "Real-time charging station operational status", "schema": {"@type": "Object", "fields": [{"name": "operationalStatus", "displayName": "Operational Status", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Online", "enumValue": "online"}, {"name": "Offline", "enumValue": "offline"}, {"name": "Maintenance", "enumValue": "maintenance"}, {"name": "Error", "enumValue": "error"}]}}, {"name": "availableConnectors", "displayName": "Available Connectors", "schema": "integer"}, {"name": "occupiedConnectors", "displayName": "Occupied Connectors", "schema": "integer"}, {"name": "totalEnergyDelivered", "displayName": "Total Energy Delivered (kWh)", "schema": "double"}, {"name": "currentLoad", "displayName": "Current Load (kW)", "schema": "double"}, {"name": "peakLoad", "displayName": "Peak Load (kW)", "schema": "double"}, {"name": "utilizationRate", "displayName": "Utilization Rate (%)", "schema": "double"}]}}, {"@type": "Telemetry", "name": "gridIntegration", "displayName": "Smart Grid Integration", "description": "Grid connection and energy management data", "schema": {"@type": "Object", "fields": [{"name": "gridVoltage", "displayName": "Grid Voltage (V)", "schema": "double"}, {"name": "gridFrequency", "displayName": "Grid Frequency (Hz)", "schema": "double"}, {"name": "powerFactor", "displayName": "Power Factor", "schema": "double"}, {"name": "energyPrice", "displayName": "Current Energy Price ($/kWh)", "schema": "double"}, {"name": "renewableEnergyPercentage", "displayName": "Renewable Energy Percentage (%)", "schema": "double"}, {"name": "carbonIntensity", "displayName": "Carbon Intensity (gCO2/kWh)", "schema": "double"}, {"name": "demandResponseActive", "displayName": "Demand Response Active", "schema": "boolean"}, {"name": "loadBalancingMode", "displayName": "Load Balancing Mode", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Static", "enumValue": "static"}, {"name": "Dynamic", "enumValue": "dynamic"}, {"name": "Smart", "enumValue": "smart"}]}}]}}, {"@type": "Telemetry", "name": "connectorDetails", "displayName": "Individual Connector Status", "description": "Detailed status of each charging connector", "schema": {"@type": "Array", "elementSchema": {"@type": "Object", "fields": [{"name": "connectorId", "displayName": "Connector ID", "schema": "string"}, {"name": "connectorType", "displayName": "Connector Type", "schema": "string"}, {"name": "status", "displayName": "Connector Status", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Available", "enumValue": "available"}, {"name": "Occupied", "enumValue": "occupied"}, {"name": "Reserved", "enumValue": "reserved"}, {"name": "Faulted", "enumValue": "faulted"}, {"name": "Unavailable", "enumValue": "unavailable"}]}}, {"name": "currentPower", "displayName": "Current Power Output (kW)", "schema": "double"}, {"name": "connectedVehicleId", "displayName": "Connected Vehicle ID", "schema": "string"}, {"name": "sessionStartTime", "displayName": "Session Start Time", "schema": "dateTime"}, {"name": "energyDelivered", "displayName": "Energy Delivered This Session (kWh)", "schema": "double"}]}}}, {"@type": "Command", "name": "optimizeChargingSchedule", "displayName": "Optimize Charging Schedule", "description": "AI-powered optimization of charging schedules across all connectors", "request": {"name": "optimizationRequest", "schema": {"@type": "Object", "fields": [{"name": "optimizationGoal", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "MinimizeCost", "enumValue": "minimize_cost"}, {"name": "MaximizeRenewable", "enumValue": "maximize_renewable"}, {"name": "BalanceLoad", "enumValue": "balance_load"}, {"name": "MinimizeWaitTime", "enumValue": "minimize_wait_time"}]}}, {"name": "timeHorizon", "displayName": "Optimization Time Horizon (hours)", "schema": "integer"}]}}, "response": {"name": "optimizedSchedule", "schema": {"@type": "Object", "fields": [{"name": "scheduleId", "schema": "string"}, {"name": "estimatedCostSavings", "schema": "double"}, {"name": "estimatedEmissionReduction", "schema": "double"}, {"name": "recommendedActions", "schema": {"@type": "Array", "elementSchema": "string"}}]}}}, {"@type": "Command", "name": "reserveConnector", "displayName": "Reserve Connector", "description": "Reserve a charging connector for a specific vehicle", "request": {"name": "reservationRequest", "schema": {"@type": "Object", "fields": [{"name": "vehicleId", "schema": "string"}, {"name": "connectorType", "schema": "string"}, {"name": "arrivalTime", "schema": "dateTime"}, {"name": "chargingDuration", "displayName": "Expected Charging Duration (minutes)", "schema": "integer"}]}}, "response": {"name": "reservationResult", "schema": {"@type": "Object", "fields": [{"name": "reservationId", "schema": "string"}, {"name": "connectorId", "schema": "string"}, {"name": "estimatedCost", "schema": "double"}, {"name": "reservationExpiry", "schema": "dateTime"}]}}}, {"@type": "Command", "name": "predictDemand", "displayName": "Predict Charging <PERSON>", "description": "AI-powered prediction of charging demand patterns", "request": {"name": "demandPredictionRequest", "schema": {"@type": "Object", "fields": [{"name": "predictionHorizon", "displayName": "Prediction Horizon (hours)", "schema": "integer"}, {"name": "includeWeatherData", "schema": "boolean"}, {"name": "includeEventData", "schema": "boolean"}]}}, "response": {"name": "demandPrediction", "schema": {"@type": "Object", "fields": [{"name": "predictedPeakDemand", "schema": "double"}, {"name": "predictedPeakTime", "schema": "dateTime"}, {"name": "utilizationForecast", "schema": {"@type": "Array", "elementSchema": "double"}}, {"name": "confidence", "schema": "double"}]}}}, {"@type": "Relationship", "name": "connectedVehicles", "displayName": "Connected Vehicles", "target": "dtmi:fleetmanagement:ElectricVehicle;1"}, {"@type": "Relationship", "name": "chargeOptimizer", "displayName": "Charge Optimizer", "target": "dtmi:fleetmanagement:EVChargeOptimizer;1"}, {"@type": "Relationship", "name": "gridConnection", "displayName": "Grid Connection", "target": "dtmi:fleetmanagement:SmartGrid;1"}]}