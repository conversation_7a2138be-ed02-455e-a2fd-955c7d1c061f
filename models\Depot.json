{"@context": "dtmi:dtdl:context;3", "@id": "dtmi:fleetmanagement:Depot;1", "@type": "Interface", "displayName": "Fleet Depot", "description": "Fleet depot/warehouse facility with vehicle management, maintenance, and logistics capabilities", "contents": [{"@type": "Property", "name": "depotId", "displayName": "Depot ID", "description": "Unique identifier for the depot", "schema": "string"}, {"@type": "Property", "name": "depotName", "displayName": "Depot Name", "description": "Name of the depot facility", "schema": "string"}, {"@type": "Property", "name": "location", "displayName": "Depot Location", "description": "Geographic location of the depot", "schema": {"@type": "Object", "fields": [{"name": "latitude", "displayName": "Latitude", "schema": "double"}, {"name": "longitude", "displayName": "Longitude", "schema": "double"}, {"name": "address", "displayName": "Street Address", "schema": "string"}, {"name": "city", "displayName": "City", "schema": "string"}, {"name": "postalCode", "displayName": "Postal Code", "schema": "string"}, {"name": "country", "displayName": "Country", "schema": "string"}]}}, {"@type": "Property", "name": "depotType", "displayName": "Depot Type", "description": "Type of depot facility", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "MainDepot", "displayName": "Main Depot", "enumValue": "main_depot"}, {"name": "SatelliteDepot", "displayName": "Satellite Depot", "enumValue": "satellite_depot"}, {"name": "MaintenanceHub", "displayName": "Maintenance Hub", "enumValue": "maintenance_hub"}, {"name": "Charg<PERSON><PERSON><PERSON>", "displayName": "Charging <PERSON>", "enumValue": "charging_hub"}, {"name": "LogisticsCenter", "displayName": "Logistics Center", "enumValue": "logistics_center"}]}}, {"@type": "Property", "name": "capacity", "displayName": "Vehicle Capacity", "description": "Maximum number of vehicles the depot can accommodate", "schema": "integer"}, {"@type": "Property", "name": "operatingHours", "displayName": "Operating Hours", "description": "Daily operating hours of the depot", "schema": {"@type": "Object", "fields": [{"name": "openTime", "displayName": "Opening Time", "schema": "time"}, {"name": "closeTime", "displayName": "Closing Time", "schema": "time"}, {"name": "operatingDays", "displayName": "Operating Days", "schema": {"@type": "Array", "elementSchema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Monday", "enumValue": "monday"}, {"name": "Tuesday", "enumValue": "tuesday"}, {"name": "Wednesday", "enumValue": "wednesday"}, {"name": "Thursday", "enumValue": "thursday"}, {"name": "Friday", "enumValue": "friday"}, {"name": "Saturday", "enumValue": "saturday"}, {"name": "Sunday", "enumValue": "sunday"}]}}}, {"name": "is24Hour", "displayName": "24-Hour Operation", "schema": "boolean"}]}}, {"@type": "Property", "name": "facilities", "displayName": "Available Facilities", "description": "List of facilities available at the depot", "schema": {"@type": "Array", "elementSchema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "MaintenanceBay", "enumValue": "maintenance_bay"}, {"name": "WashingStation", "enumValue": "washing_station"}, {"name": "FuelingStation", "enumValue": "fueling_station"}, {"name": "ChargingStation", "enumValue": "charging_station"}, {"name": "PartsWarehouse", "enumValue": "parts_warehouse"}, {"name": "DriverFacilities", "enumValue": "driver_facilities"}, {"name": "SecuritySystem", "enumValue": "security_system"}, {"name": "WeighBridge", "enumValue": "weigh_bridge"}]}}}, {"@type": "Telemetry", "name": "depotStatus", "displayName": "Depot Operational Status", "description": "Real-time depot operational status and metrics", "schema": {"@type": "Object", "fields": [{"name": "operationalStatus", "displayName": "Operational Status", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Operational", "enumValue": "operational"}, {"name": "PartialOperation", "enumValue": "partial_operation"}, {"name": "Maintenance", "enumValue": "maintenance"}, {"name": "Closed", "enumValue": "closed"}, {"name": "Emergency", "enumValue": "emergency"}]}}, {"name": "currentOccupancy", "displayName": "Current Vehicle Occupancy", "schema": "integer"}, {"name": "occupancyRate", "displayName": "Occupancy Rate (%)", "schema": "double"}, {"name": "vehiclesInMaintenance", "displayName": "Vehicles in Maintenance", "schema": "integer"}, {"name": "vehiclesCharging", "displayName": "Vehicles Charging", "schema": "integer"}, {"name": "vehiclesRefueling", "displayName": "Vehicles Refueling", "schema": "integer"}, {"name": "staff<PERSON>n<PERSON>uty", "displayName": "Staff on Duty", "schema": "integer"}, {"name": "securityStatus", "displayName": "Security Status", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Secure", "enumValue": "secure"}, {"name": "<PERSON><PERSON>", "enumValue": "alert"}, {"name": "<PERSON><PERSON><PERSON>", "enumValue": "breach"}, {"name": "Maintenance", "enumValue": "maintenance"}]}}]}}, {"@type": "Telemetry", "name": "facilityUtilization", "displayName": "Facility Utilization Metrics", "description": "Utilization metrics for various depot facilities", "schema": {"@type": "Object", "fields": [{"name": "maintenanceBayUtilization", "displayName": "Maintenance Bay Utilization (%)", "schema": "double"}, {"name": "chargingStationUtilization", "displayName": "Charging Station Utilization (%)", "schema": "double"}, {"name": "fuelingStationUtilization", "displayName": "Fueling Station Utilization (%)", "schema": "double"}, {"name": "washingStationUtilization", "displayName": "Washing Station Utilization (%)", "schema": "double"}, {"name": "averageServiceTime", "displayName": "Average Service Time (minutes)", "schema": "double"}, {"name": "queueLength", "displayName": "Current Queue Length", "schema": "integer"}, {"name": "averageWaitTime", "displayName": "Average Wait Time (minutes)", "schema": "double"}, {"name": "throughput", "displayName": "Daily Vehicle Throughput", "schema": "integer"}]}}, {"@type": "Telemetry", "name": "environmentalConditions", "displayName": "Environmental Conditions", "description": "Environmental monitoring within the depot", "schema": {"@type": "Object", "fields": [{"name": "temperature", "displayName": "Temperature (°C)", "schema": "double"}, {"name": "humidity", "displayName": "Humidity (%)", "schema": "double"}, {"name": "airQuality", "displayName": "Air Quality Index", "schema": "double"}, {"name": "noiseLevel", "displayName": "Noise Level (dB)", "schema": "double"}, {"name": "lightingLevel", "displayName": "Lighting Level (lux)", "schema": "double"}, {"name": "ventilationStatus", "displayName": "Ventilation System Status", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Optimal", "enumValue": "optimal"}, {"name": "Adequate", "enumValue": "adequate"}, {"name": "Poor", "enumValue": "poor"}, {"name": "Maintenance Required", "enumValue": "maintenance_required"}]}}]}}, {"@type": "Telemetry", "name": "energyManagement", "displayName": "Energy Management", "description": "Energy consumption and management metrics", "schema": {"@type": "Object", "fields": [{"name": "totalEnergyConsumption", "displayName": "Total Energy Consumption (kWh)", "schema": "double"}, {"name": "chargingEnergyConsumption", "displayName": "Charging Energy Consumption (kWh)", "schema": "double"}, {"name": "facilityEnergyConsumption", "displayName": "Facility Energy Consumption (kWh)", "schema": "double"}, {"name": "renewableEnergyUsage", "displayName": "Renewable Energy Usage (%)", "schema": "double"}, {"name": "energyEfficiencyScore", "displayName": "Energy Efficiency Score (0-100)", "schema": "double"}, {"name": "peakDemand", "displayName": "Peak Demand (kW)", "schema": "double"}, {"name": "loadFactor", "displayName": "Load Factor (%)", "schema": "double"}, {"name": "energyCost", "displayName": "Daily Energy Cost ($)", "schema": "double"}]}}, {"@type": "Command", "name": "scheduleVehicleService", "displayName": "Schedule Vehicle Service", "description": "Schedule vehicle service at the depot", "request": {"name": "serviceRequest", "schema": {"@type": "Object", "fields": [{"name": "vehicleId", "schema": "string"}, {"name": "serviceType", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Maintenance", "enumValue": "maintenance"}, {"name": "Repair", "enumValue": "repair"}, {"name": "Inspection", "enumValue": "inspection"}, {"name": "Cleaning", "enumValue": "cleaning"}, {"name": "Refueling", "enumValue": "refueling"}, {"name": "Charging", "enumValue": "charging"}]}}, {"name": "priority", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Low", "enumValue": "low"}, {"name": "Normal", "enumValue": "normal"}, {"name": "High", "enumValue": "high"}, {"name": "Emergency", "enumValue": "emergency"}]}}, {"name": "estimatedDuration", "displayName": "Estimated Duration (minutes)", "schema": "integer"}, {"name": "preferredTime", "displayName": "Preferred Service Time", "schema": "dateTime"}]}}, "response": {"name": "serviceSchedule", "schema": {"@type": "Object", "fields": [{"name": "serviceId", "schema": "string"}, {"name": "scheduledTime", "schema": "dateTime"}, {"name": "assignedBay", "schema": "string"}, {"name": "estimatedCompletion", "schema": "dateTime"}, {"name": "queuePosition", "schema": "integer"}]}}}, {"@type": "Command", "name": "optimizeDepotOperations", "displayName": "Optimize Depot Operations", "description": "AI-powered optimization of depot operations and resource allocation", "request": {"name": "optimizationRequest", "schema": {"@type": "Object", "fields": [{"name": "optimizationGoal", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "MinimizeWaitTime", "enumValue": "minimize_wait_time"}, {"name": "MaximizeUtilization", "enumValue": "maximize_utilization"}, {"name": "MinimizeEnergyCost", "enumValue": "minimize_energy_cost"}, {"name": "BalanceWorkload", "enumValue": "balance_workload"}]}}, {"name": "timeHorizon", "displayName": "Optimization Time Horizon (hours)", "schema": "integer"}, {"name": "includeStaffScheduling", "schema": "boolean"}, {"name": "includeEnergyOptimization", "schema": "boolean"}]}}, "response": {"name": "optimizationResult", "schema": {"@type": "Object", "fields": [{"name": "optimizationId", "schema": "string"}, {"name": "recommendations", "schema": {"@type": "Array", "elementSchema": "string"}}, {"name": "expectedImprovement", "schema": "double"}, {"name": "implementationSteps", "schema": {"@type": "Array", "elementSchema": "string"}}]}}}, {"@type": "Command", "name": "generateDepotReport", "displayName": "Generate Depot Performance Report", "description": "Generate comprehensive depot performance and utilization report", "request": {"name": "reportRequest", "schema": {"@type": "Object", "fields": [{"name": "reportType", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Operational", "enumValue": "operational"}, {"name": "Utilization", "enumValue": "utilization"}, {"name": "Energy", "enumValue": "energy"}, {"name": "Maintenance", "enumValue": "maintenance"}, {"name": "Comprehensive", "enumValue": "comprehensive"}]}}, {"name": "timeRange", "schema": {"@type": "Object", "fields": [{"name": "startDate", "schema": "dateTime"}, {"name": "endDate", "schema": "dateTime"}]}}, {"name": "includeRecommendations", "schema": "boolean"}]}}}, {"@type": "Relationship", "name": "hostedVehicles", "displayName": "Hosted Vehicles", "target": "dtmi:fleetmanagement:Vehicle;1"}, {"@type": "Relationship", "name": "chargingStations", "displayName": "Charging Stations", "target": "dtmi:fleetmanagement:ChargingStation;1"}, {"@type": "Relationship", "name": "maintenanceSchedules", "displayName": "Maintenance Schedules", "target": "dtmi:fleetmanagement:MaintenanceSchedule;1"}, {"@type": "Relationship", "name": "depotStaff", "displayName": "Depot Staff", "target": "dtmi:fleetmanagement:DepotStaff;1"}]}