{"@context": "dtmi:dtdl:context;3", "@id": "dtmi:fleetmanagement:Driver;1", "@type": "Interface", "displayName": "Fleet Driver", "description": "Driver digital twin with behavior analysis and performance tracking", "contents": [{"@type": "Property", "name": "driverId", "displayName": "Driver ID", "description": "Unique identifier for the driver", "schema": "string"}, {"@type": "Property", "name": "employeeId", "displayName": "Employee ID", "description": "Company employee identifier", "schema": "string"}, {"@type": "Property", "name": "firstName", "displayName": "First Name", "description": "Driver's first name", "schema": "string"}, {"@type": "Property", "name": "lastName", "displayName": "Last Name", "description": "Driver's last name", "schema": "string"}, {"@type": "Property", "name": "licenseNumber", "displayName": "License Number", "description": "Driver's license number", "schema": "string"}, {"@type": "Property", "name": "licenseClass", "displayName": "License Class", "description": "Type of driving license", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "ClassA", "displayName": "Class A (Heavy Trucks)", "enumValue": "class_a"}, {"name": "ClassB", "displayName": "Class B (Large Trucks)", "enumValue": "class_b"}, {"name": "ClassC", "displayName": "Class C (Regular Vehicles)", "enumValue": "class_c"}, {"name": "Motorcycle", "displayName": "Motorcycle License", "enumValue": "motorcycle"}]}}, {"@type": "Property", "name": "experienceYears", "displayName": "Years of Experience", "description": "Years of driving experience", "schema": "integer"}, {"@type": "Property", "name": "status", "displayName": "Driver Status", "description": "Current employment status", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Active", "enumValue": "active"}, {"name": "OnLeave", "enumValue": "on_leave"}, {"name": "Training", "enumValue": "training"}, {"name": "Suspended", "enumValue": "suspended"}, {"name": "Inactive", "enumValue": "inactive"}]}}, {"@type": "Telemetry", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "displayName": "Driving Behavior Metrics", "description": "Real-time driving behavior analysis", "schema": {"@type": "Object", "fields": [{"name": "averageSpeed", "displayName": "Average Speed (km/h)", "schema": "double"}, {"name": "maxSpeed", "displayName": "Maximum Speed (km/h)", "schema": "double"}, {"name": "harshAccelerationEvents", "displayName": "Harsh Acceleration Events", "schema": "integer"}, {"name": "harshBrakingEvents", "displayName": "Harsh Braking Events", "schema": "integer"}, {"name": "sharpTurnEvents", "displayName": "Sharp Turn Events", "schema": "integer"}, {"name": "speedingEvents", "displayName": "Speeding Events", "schema": "integer"}, {"name": "idlingTime", "displayName": "Idling Time (minutes)", "schema": "double"}, {"name": "fuelEfficiencyScore", "displayName": "Fuel Efficiency Score (0-100)", "schema": "double"}, {"name": "safetyScore", "displayName": "AI Safety Score (0-100)", "schema": "double"}, {"name": "ecoScore", "displayName": "Eco-Driving Score (0-100)", "schema": "double"}]}}, {"@type": "Telemetry", "name": "workingHours", "displayName": "Working Hours Tracking", "description": "Driver working hours and compliance monitoring", "schema": {"@type": "Object", "fields": [{"name": "shiftStartTime", "displayName": "Shift Start Time", "schema": "dateTime"}, {"name": "shiftEndTime", "displayName": "Shift End Time", "schema": "dateTime"}, {"name": "totalDrivingTime", "displayName": "Total Driving Time (hours)", "schema": "double"}, {"name": "breakTime", "displayName": "Break Time (hours)", "schema": "double"}, {"name": "overtimeHours", "displayName": "Overtime Hours", "schema": "double"}, {"name": "complianceStatus", "displayName": "Hours of Service Compliance", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Compliant", "enumValue": "compliant"}, {"name": "Warning", "enumValue": "warning"}, {"name": "Violation", "enumValue": "violation"}]}}]}}, {"@type": "Telemetry", "name": "performanceMetrics", "displayName": "Performance Metrics", "description": "Driver performance and productivity metrics", "schema": {"@type": "Object", "fields": [{"name": "totalMilesDriven", "displayName": "Total Miles Driven", "schema": "double"}, {"name": "deliveriesCompleted", "displayName": "Deliveries Completed", "schema": "integer"}, {"name": "onTimeDeliveryRate", "displayName": "On-Time Delivery Rate (%)", "schema": "double"}, {"name": "customerRating", "displayName": "Customer Rating (1-5)", "schema": "double"}, {"name": "incidentCount", "displayName": "Incident Count", "schema": "integer"}, {"name": "trainingCompletionRate", "displayName": "Training Completion Rate (%)", "schema": "double"}, {"name": "overallPerformanceScore", "displayName": "AI Overall Performance Score (0-100)", "schema": "double"}]}}, {"@type": "Command", "name": "generatePerformanceReport", "displayName": "Generate Performance Report", "description": "Generate AI-powered driver performance analysis", "request": {"name": "reportRequest", "schema": {"@type": "Object", "fields": [{"name": "reportPeriod", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Daily", "enumValue": "daily"}, {"name": "Weekly", "enumValue": "weekly"}, {"name": "Monthly", "enumValue": "monthly"}, {"name": "Quarterly", "enumValue": "quarterly"}]}}, {"name": "includeRecommendations", "schema": "boolean"}]}}, "response": {"name": "performanceReport", "schema": {"@type": "Object", "fields": [{"name": "overallScore", "schema": "double"}, {"name": "strengths", "schema": {"@type": "Array", "elementSchema": "string"}}, {"name": "improvementAreas", "schema": {"@type": "Array", "elementSchema": "string"}}, {"name": "trainingRecommendations", "schema": {"@type": "Array", "elementSchema": "string"}}]}}}, {"@type": "Command", "name": "scheduleTraining", "displayName": "Schedule Training", "description": "Schedule personalized training based on AI analysis", "request": {"name": "trainingRequest", "schema": {"@type": "Object", "fields": [{"name": "trainingType", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "SafetyTraining", "enumValue": "safety"}, {"name": "EcoDriverTraining", "enumValue": "eco_driving"}, {"name": "DefensiveDriverTraining", "enumValue": "defensive_driving"}, {"name": "VehicleSpecificTraining", "enumValue": "vehicle_specific"}, {"name": "ComplianceTraining", "enumValue": "compliance"}]}}, {"name": "urgency", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Low", "enumValue": "low"}, {"name": "Medium", "enumValue": "medium"}, {"name": "High", "enumValue": "high"}, {"name": "Mandatory", "enumValue": "mandatory"}]}}]}}}, {"@type": "Relationship", "name": "assignedVehicles", "displayName": "Assigned Vehicles", "target": "dtmi:fleetmanagement:Vehicle;1"}, {"@type": "Relationship", "name": "currentRoute", "displayName": "Current Route", "target": "dtmi:fleetmanagement:Route;1"}, {"@type": "Relationship", "name": "behaviorAnalyzer", "displayName": "Driver Behavior Analyzer", "target": "dtmi:fleetmanagement:DriverBehaviorAnalyzer;1"}]}