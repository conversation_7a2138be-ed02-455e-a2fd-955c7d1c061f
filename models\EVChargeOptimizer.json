{"@context": "dtmi:dtdl:context;3", "@id": "dtmi:fleetmanagement:EVChargeOptimizer;1", "@type": "Interface", "displayName": "EV Charge Optimizer", "description": "AI-powered electric vehicle charging optimization with smart grid integration and cost minimization", "contents": [{"@type": "Property", "name": "optimizerId", "displayName": "Optimizer ID", "description": "Unique identifier for the EV charge optimizer", "schema": "string"}, {"@type": "Property", "name": "optimizerName", "displayName": "Optimizer Name", "description": "Name of the charging optimizer instance", "schema": "string"}, {"@type": "Property", "name": "optimizationAlgorithm", "displayName": "Optimization Algorithm", "description": "Type of optimization algorithm used", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "LinearProgramming", "enumValue": "linear_programming"}, {"name": "GeneticAlgorithm", "enumValue": "genetic_algorithm"}, {"name": "ParticleSwarmOptimization", "enumValue": "pso"}, {"name": "ReinforcementLearning", "enumValue": "reinforcement_learning"}, {"name": "MixedIntegerProgramming", "enumValue": "mixed_integer_programming"}, {"name": "DeepQLearning", "enumValue": "deep_q_learning"}]}}, {"@type": "Property", "name": "optimizationObjectives", "displayName": "Optimization Objectives", "description": "Primary objectives for charging optimization", "schema": {"@type": "Array", "elementSchema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "MinimizeCost", "enumValue": "minimize_cost"}, {"name": "MaximizeRenewableUsage", "enumValue": "maximize_renewable"}, {"name": "MinimizeGridStress", "enumValue": "minimize_grid_stress"}, {"name": "MaximizeBatteryLife", "enumValue": "maximize_battery_life"}, {"name": "MinimizeChargingTime", "enumValue": "minimize_charging_time"}, {"name": "BalanceLoadDistribution", "enumValue": "balance_load"}]}}}, {"@type": "Property", "name": "timeHorizon", "displayName": "Optimization Time Horizon (hours)", "description": "Time horizon for optimization planning", "schema": "integer"}, {"@type": "Property", "name": "updateFrequency", "displayName": "Update Frequency (minutes)", "description": "How often the optimization is recalculated", "schema": "integer"}, {"@type": "Telemetry", "name": "optimizationResults", "displayName": "Current Optimization Results", "description": "Latest charging optimization results and recommendations", "schema": {"@type": "Object", "fields": [{"name": "optimizationId", "displayName": "Optimization Run ID", "schema": "string"}, {"name": "timestamp", "displayName": "Optimization Timestamp", "schema": "dateTime"}, {"name": "totalVehiclesOptimized", "displayName": "Total Vehicles Optimized", "schema": "integer"}, {"name": "totalChargingStations", "displayName": "Total Charging Stations", "schema": "integer"}, {"name": "optimizationStatus", "displayName": "Optimization Status", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Optimal", "enumValue": "optimal"}, {"name": "Feasible", "enumValue": "feasible"}, {"name": "Infeasible", "enumValue": "infeasible"}, {"name": "Unbounded", "enumValue": "unbounded"}, {"name": "InProgress", "enumValue": "in_progress"}]}}, {"name": "objectiveValue", "displayName": "Objective Function Value", "schema": "double"}, {"name": "estimatedCostSavings", "displayName": "Estimated Cost Savings ($)", "schema": "double"}, {"name": "estimatedEmissionReduction", "displayName": "Estimated CO2 Reduction (kg)", "schema": "double"}, {"name": "renewableEnergyUsage", "displayName": "Renewable Energy Usage (%)", "schema": "double"}, {"name": "peakLoadReduction", "displayName": "Peak Load Reduction (%)", "schema": "double"}, {"name": "averageChargingEfficiency", "displayName": "Average Charging Efficiency (%)", "schema": "double"}]}}, {"@type": "Telemetry", "name": "chargingSchedule", "displayName": "Optimized Charging Schedule", "description": "Detailed charging schedule for all vehicles", "schema": {"@type": "Array", "elementSchema": {"@type": "Object", "fields": [{"name": "vehicleId", "displayName": "Vehicle ID", "schema": "string"}, {"name": "stationId", "displayName": "Assigned Charging Station ID", "schema": "string"}, {"name": "connectorId", "displayName": "Assigned Connector ID", "schema": "string"}, {"name": "scheduledStartTime", "displayName": "Scheduled Start Time", "schema": "dateTime"}, {"name": "scheduledEndTime", "displayName": "Scheduled End Time", "schema": "dateTime"}, {"name": "chargingPower", "displayName": "Optimal Charging Power (kW)", "schema": "double"}, {"name": "targetSoC", "displayName": "Target State of Charge (%)", "schema": "double"}, {"name": "estimatedCost", "displayName": "Estimated Charging Cost ($)", "schema": "double"}, {"name": "priority", "displayName": "Charging Priority", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Low", "enumValue": "low"}, {"name": "Normal", "enumValue": "normal"}, {"name": "High", "enumValue": "high"}, {"name": "Critical", "enumValue": "critical"}]}}, {"name": "flexibilityWindow", "displayName": "Flexibility Window (minutes)", "schema": "integer"}, {"name": "chargingStrategy", "displayName": "Charging Strategy", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "FastCharging", "enumValue": "fast_charging"}, {"name": "EconomyCharging", "enumValue": "economy_charging"}, {"name": "BatteryOptimal", "enumValue": "battery_optimal"}, {"name": "GridOptimal", "enumValue": "grid_optimal"}]}}]}}}, {"@type": "Telemetry", "name": "gridIntegrationMetrics", "displayName": "Smart Grid Integration Metrics", "description": "Metrics related to smart grid integration and demand response", "schema": {"@type": "Object", "fields": [{"name": "currentGridLoad", "displayName": "Current Grid Load (MW)", "schema": "double"}, {"name": "predictedGridLoad", "displayName": "Predicted <PERSON><PERSON> (MW)", "schema": "double"}, {"name": "renewableGeneration", "displayName": "Current Renewable Generation (MW)", "schema": "double"}, {"name": "gridStabilityIndex", "displayName": "Grid Stability Index (0-100)", "schema": "double"}, {"name": "demandResponseEvents", "displayName": "Active Demand Response Events", "schema": "integer"}, {"name": "loadShiftingPotential", "displayName": "Load Shifting Potential (kWh)", "schema": "double"}, {"name": "v2gCapacity", "displayName": "Vehicle-to-Grid Capacity (kW)", "schema": "double"}, {"name": "energyStorageUtilization", "displayName": "Energy Storage Utilization (%)", "schema": "double"}]}}, {"@type": "Telemetry", "name": "realTimeAdjustments", "displayName": "Real-time Schedule Adjustments", "description": "Dynamic adjustments to charging schedules based on real-time conditions", "schema": {"@type": "Array", "elementSchema": {"@type": "Object", "fields": [{"name": "adjustmentId", "schema": "string"}, {"name": "vehicleId", "schema": "string"}, {"name": "adjustmentType", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "PowerReduction", "enumValue": "power_reduction"}, {"name": "PowerIncrease", "enumValue": "power_increase"}, {"name": "TimeShift", "enumValue": "time_shift"}, {"name": "StationChange", "enumValue": "station_change"}, {"name": "ChargingPause", "enumValue": "charging_pause"}, {"name": "V2GActivation", "enumValue": "v2g_activation"}]}}, {"name": "reason", "schema": "string"}, {"name": "adjustmentTimestamp", "schema": "dateTime"}, {"name": "impactOnCost", "schema": "double"}, {"name": "impactOnSchedule", "schema": "double"}]}}}, {"@type": "Command", "name": "optimizeChargingSchedule", "displayName": "Optimize Charging Schedule", "description": "Run comprehensive charging optimization for the fleet", "request": {"name": "optimizationRequest", "schema": {"@type": "Object", "fields": [{"name": "optimizationScope", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "FleetWide", "enumValue": "fleet_wide"}, {"name": "SpecificVehicles", "enumValue": "specific_vehicles"}, {"name": "SpecificStations", "enumValue": "specific_stations"}, {"name": "DepotOnly", "enumValue": "depot_only"}]}}, {"name": "vehicleIds", "displayName": "Specific Vehicle IDs (if applicable)", "schema": {"@type": "Array", "elementSchema": "string"}}, {"name": "timeHorizon", "displayName": "Optimization Time Horizon (hours)", "schema": "integer"}, {"name": "objectiveWeights", "displayName": "Objective Function Weights", "schema": {"@type": "Object", "fields": [{"name": "cost", "schema": "double"}, {"name": "renewable", "schema": "double"}, {"name": "gridStability", "schema": "double"}, {"name": "batteryHealth", "schema": "double"}, {"name": "operationalEfficiency", "schema": "double"}]}}, {"name": "constraints", "displayName": "Optimization Constraints", "schema": {"@type": "Object", "fields": [{"name": "maxPowerDraw", "schema": "double"}, {"name": "minSoCRequirement", "schema": "double"}, {"name": "departureDeadlines", "schema": {"@type": "Array", "elementSchema": {"@type": "Object", "fields": [{"name": "vehicleId", "schema": "string"}, {"name": "deadline", "schema": "dateTime"}]}}}]}}]}}, "response": {"name": "optimizationResult", "schema": {"@type": "Object", "fields": [{"name": "optimizationId", "schema": "string"}, {"name": "status", "schema": "string"}, {"name": "objectiveValue", "schema": "double"}, {"name": "solutionQuality", "schema": "double"}, {"name": "computationTime", "schema": "double"}, {"name": "scheduledVehicles", "schema": "integer"}, {"name": "totalEnergy", "schema": "double"}, {"name": "totalCost", "schema": "double"}, {"name": "peakPowerReduction", "schema": "double"}]}}}, {"@type": "Command", "name": "predictChargingDemand", "displayName": "Predict Charging <PERSON>", "description": "Predict future charging demand using AI forecasting models", "request": {"name": "demandPredictionRequest", "schema": {"@type": "Object", "fields": [{"name": "predictionHorizon", "displayName": "Prediction Horizon (hours)", "schema": "integer"}, {"name": "granularity", "displayName": "Time Granularity (minutes)", "schema": "integer"}, {"name": "includeWeatherForecast", "schema": "boolean"}, {"name": "includeTrafficData", "schema": "boolean"}, {"name": "includeEventData", "schema": "boolean"}]}}, "response": {"name": "demandPrediction", "schema": {"@type": "Object", "fields": [{"name": "predictionId", "schema": "string"}, {"name": "demandForecast", "schema": {"@type": "Array", "elementSchema": {"@type": "Object", "fields": [{"name": "timestamp", "schema": "dateTime"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "schema": "double"}, {"name": "confidence", "schema": "double"}, {"name": "upperBound", "schema": "double"}, {"name": "lowerBound", "schema": "double"}]}}}, {"name": "peakDemandTime", "schema": "dateTime"}, {"name": "peakDemandValue", "schema": "double"}, {"name": "modelAccuracy", "schema": "double"}]}}}, {"@type": "Command", "name": "activateDemandResponse", "displayName": "Activate Demand Response", "description": "Activate demand response protocols for grid stability", "request": {"name": "demandResponseRequest", "schema": {"@type": "Object", "fields": [{"name": "responseType", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "LoadReduction", "enumValue": "load_reduction"}, {"name": "LoadShifting", "enumValue": "load_shifting"}, {"name": "V2GDischarge", "enumValue": "v2g_discharge"}, {"name": "ChargingPause", "enumValue": "charging_pause"}]}}, {"name": "targetReduction", "displayName": "Target Load Reduction (kW)", "schema": "double"}, {"name": "duration", "displayName": "Response Duration (minutes)", "schema": "integer"}, {"name": "priority", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Low", "enumValue": "low"}, {"name": "Medium", "enumValue": "medium"}, {"name": "High", "enumValue": "high"}, {"name": "Emergency", "enumValue": "emergency"}]}}, {"name": "compensationRate", "displayName": "Compensation Rate ($/kWh)", "schema": "double"}]}}, "response": {"name": "demandResponseResult", "schema": {"@type": "Object", "fields": [{"name": "responseId", "schema": "string"}, {"name": "actualReduction", "schema": "double"}, {"name": "participatingVehicles", "schema": "integer"}, {"name": "estimatedCompensation", "schema": "double"}, {"name": "impactOnSchedule", "schema": "string"}]}}}, {"@type": "Command", "name": "generateChargingReport", "displayName": "Generate Charging Analytics Report", "description": "Generate comprehensive charging optimization and performance report", "request": {"name": "reportRequest", "schema": {"@type": "Object", "fields": [{"name": "reportType", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Performance", "enumValue": "performance"}, {"name": "CostAnalysis", "enumValue": "cost_analysis"}, {"name": "GridImpact", "enumValue": "grid_impact"}, {"name": "Sustainability", "enumValue": "sustainability"}, {"name": "Comprehensive", "enumValue": "comprehensive"}]}}, {"name": "timeRange", "schema": {"@type": "Object", "fields": [{"name": "startDate", "schema": "dateTime"}, {"name": "endDate", "schema": "dateTime"}]}}, {"name": "includeRecommendations", "schema": "boolean"}]}}}, {"@type": "Relationship", "name": "managedVehicles", "displayName": "Managed Electric Vehicles", "target": "dtmi:fleetmanagement:ElectricVehicle;1"}, {"@type": "Relationship", "name": "managedStations", "displayName": "Managed Charging Stations", "target": "dtmi:fleetmanagement:ChargingStation;1"}, {"@type": "Relationship", "name": "gridConnection", "displayName": "Smart Grid Connection", "target": "dtmi:fleetmanagement:SmartGrid;1"}, {"@type": "Relationship", "name": "demandForecaster", "displayName": "Demand Forecasting Model", "target": "dtmi:fleetmanagement:DemandForecastingModel;1"}]}