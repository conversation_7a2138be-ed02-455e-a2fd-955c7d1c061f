{"@context": "dtmi:dtdl:context;3", "@id": "dtmi:fleetmanagement:ElectricVehicle;1", "@type": "Interface", "displayName": "Electric Vehicle", "description": "Electric vehicle digital twin with battery management and charging optimization", "extends": "dtmi:fleetmanagement:Vehicle;1", "contents": [{"@type": "Property", "name": "batteryCapacity", "displayName": "Battery Capacity (kWh)", "description": "Total battery capacity in kilowatt-hours", "schema": "double"}, {"@type": "Property", "name": "maxCharging<PERSON><PERSON>er", "displayName": "Max Charging Power (kW)", "description": "Maximum charging power supported", "schema": "double"}, {"@type": "Property", "name": "chargingConnectorType", "displayName": "Charging Connector Type", "description": "Type of charging connector", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "CCS", "displayName": "Combined Charging System", "enumValue": "ccs"}, {"name": "CHAdeMO", "displayName": "CHAdeMO", "enumValue": "chademo"}, {"name": "Type2", "displayName": "Type 2 (Mennekes)", "enumValue": "type2"}, {"name": "Tesla", "displayName": "Tesla Supercharger", "enumValue": "tesla"}]}}, {"@type": "Telemetry", "name": "batteryStatus", "displayName": "Battery Status", "description": "Real-time battery performance and health data", "schema": {"@type": "Object", "fields": [{"name": "stateOfCharge", "displayName": "State of Charge (%)", "schema": "double"}, {"name": "stateOfHealth", "displayName": "State of Health (%)", "schema": "double"}, {"name": "batteryTemperature", "displayName": "Battery Temperature (°C)", "schema": "double"}, {"name": "voltage", "displayName": "Battery Voltage (V)", "schema": "double"}, {"name": "current", "displayName": "Battery Current (A)", "schema": "double"}, {"name": "power", "displayName": "Battery Power (kW)", "schema": "double"}, {"name": "estimatedRange", "displayName": "Estimated Range (km)", "schema": "double"}, {"name": "energyConsumptionRate", "displayName": "Energy Consumption (kWh/100km)", "schema": "double"}, {"name": "regenerativeBrakingEnergy", "displayName": "Regenerative Braking Energy (kWh)", "schema": "double"}]}}, {"@type": "Telemetry", "name": "chargingStatus", "displayName": "Charging Status", "description": "Real-time charging information", "schema": {"@type": "Object", "fields": [{"name": "isCharging", "displayName": "Is Charging", "schema": "boolean"}, {"name": "chargingPower", "displayName": "Charging Power (kW)", "schema": "double"}, {"name": "chargingVoltage", "displayName": "Charging Voltage (V)", "schema": "double"}, {"name": "chargingCurrent", "displayName": "Charging Current (A)", "schema": "double"}, {"name": "timeToFullCharge", "displayName": "Time to Full Charge (minutes)", "schema": "integer"}, {"name": "chargingEfficiency", "displayName": "Charging Efficiency (%)", "schema": "double"}, {"name": "chargingCost", "displayName": "Current Charging Cost", "schema": "double"}, {"name": "connectedStationId", "displayName": "Connected Charging Station ID", "schema": "string"}]}}, {"@type": "Telemetry", "name": "thermalManagement", "displayName": "Thermal Management", "description": "Battery thermal management system data", "schema": {"@type": "Object", "fields": [{"name": "coolantTemperature", "displayName": "Coolant Temperature (°C)", "schema": "double"}, {"name": "heaterStatus", "displayName": "Battery Heater Status", "schema": "boolean"}, {"name": "coolingFanSpeed", "displayName": "Cooling Fan Speed (%)", "schema": "double"}, {"name": "thermalEfficiency", "displayName": "Thermal Efficiency (%)", "schema": "double"}]}}, {"@type": "Command", "name": "optimizeCharging", "displayName": "Optimize Charging Schedule", "description": "AI-powered charging optimization based on usage patterns and grid conditions", "request": {"name": "chargingOptimizationRequest", "schema": {"@type": "Object", "fields": [{"name": "targetSoC", "displayName": "Target State of Charge (%)", "schema": "double"}, {"name": "departureTime", "displayName": "Planned Departure Time", "schema": "dateTime"}, {"name": "optimizationGoal", "displayName": "Optimization Goal", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Cost", "enumValue": "cost"}, {"name": "Speed", "enumValue": "speed"}, {"name": "BatteryHealth", "enumValue": "battery_health"}, {"name": "GridStability", "enumValue": "grid_stability"}]}}]}}, "response": {"name": "chargingPlan", "schema": {"@type": "Object", "fields": [{"name": "optimalStartTime", "schema": "dateTime"}, {"name": "optimalEndTime", "schema": "dateTime"}, {"name": "recommended<PERSON>ower", "schema": "double"}, {"name": "estimatedCost", "schema": "double"}, {"name": "estimatedDuration", "schema": "integer"}]}}}, {"@type": "Command", "name": "predictRange", "displayName": "Predict Driving Range", "description": "AI-powered range prediction based on route, weather, and driving patterns", "request": {"name": "rangePredictionRequest", "schema": {"@type": "Object", "fields": [{"name": "destinationLatitude", "schema": "double"}, {"name": "destinationLongitude", "schema": "double"}, {"name": "weatherConditions", "schema": "string"}, {"name": "drivingStyle", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Eco", "enumValue": "eco"}, {"name": "Normal", "enumValue": "normal"}, {"name": "Sport", "enumValue": "sport"}]}}]}}, "response": {"name": "rangePrediction", "schema": {"@type": "Object", "fields": [{"name": "estimatedRange", "schema": "double"}, {"name": "confidence", "schema": "double"}, {"name": "recommendedChargingStops", "schema": {"@type": "Array", "elementSchema": "string"}}]}}}, {"@type": "Command", "name": "preconditionBattery", "displayName": "Precondition Battery", "description": "Optimize battery temperature for charging or driving", "request": {"name": "preconditioningRequest", "schema": {"@type": "Object", "fields": [{"name": "targetTemperature", "schema": "double"}, {"name": "preconditioningType", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Charging", "enumValue": "charging"}, {"name": "Driving", "enumValue": "driving"}]}}]}}}, {"@type": "Relationship", "name": "preferredChargingStation", "displayName": "Preferred Charging Station", "target": "dtmi:fleetmanagement:ChargingStation;1"}, {"@type": "Relationship", "name": "chargingOptimizer", "displayName": "EV Charging Optimizer", "target": "dtmi:fleetmanagement:EVChargeOptimizer;1"}, {"@type": "Relationship", "name": "batteryHealthModel", "displayName": "Battery Health Prediction Model", "target": "dtmi:fleetmanagement:BatteryHealthModel;1"}]}