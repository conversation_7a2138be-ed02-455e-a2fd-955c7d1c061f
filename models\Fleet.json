{"@context": "dtmi:dtdl:context;3", "@id": "dtmi:fleetmanagement:Fleet;1", "@type": "Interface", "displayName": "Fleet Management System", "description": "Root digital twin for fleet management with AI-powered analytics and optimization", "contents": [{"@type": "Property", "name": "fleetId", "displayName": "Fleet ID", "description": "Unique identifier for the fleet", "schema": "string"}, {"@type": "Property", "name": "fleetName", "displayName": "Fleet Name", "description": "Name of the fleet organization", "schema": "string"}, {"@type": "Property", "name": "totalVehicles", "displayName": "Total Vehicles", "description": "Total number of vehicles in the fleet", "schema": "integer"}, {"@type": "Property", "name": "activeVehicles", "displayName": "Active Vehicles", "description": "Number of currently active vehicles", "schema": "integer"}, {"@type": "Property", "name": "totalDrivers", "displayName": "Total Drivers", "description": "Total number of drivers in the fleet", "schema": "integer"}, {"@type": "Property", "name": "operationalStatus", "displayName": "Operational Status", "description": "Current operational status of the fleet", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Operational", "displayName": "Operational", "enumValue": "operational"}, {"name": "Maintenance", "displayName": "Under Maintenance", "enumValue": "maintenance"}, {"name": "Emergency", "displayName": "Emergency Mode", "enumValue": "emergency"}]}}, {"@type": "Telemetry", "name": "fleetMetrics", "displayName": "Fleet Metrics", "description": "Real-time fleet performance metrics", "schema": {"@type": "Object", "fields": [{"name": "totalMileage", "displayName": "Total Mileage", "schema": "double"}, {"name": "fuelConsumption", "displayName": "Total Fuel Consumption", "schema": "double"}, {"name": "energyConsumption", "displayName": "Total Energy Consumption (kWh)", "schema": "double"}, {"name": "averageUtilization", "displayName": "Average Vehicle Utilization %", "schema": "double"}, {"name": "maintenanceCosts", "displayName": "Total Maintenance Costs", "schema": "double"}, {"name": "operationalCosts", "displayName": "Total Operational Costs", "schema": "double"}, {"name": "co2Emissions", "displayName": "CO2 Emissions (kg)", "schema": "double"}]}}, {"@type": "Command", "name": "optimizeFleetOperations", "displayName": "Optimize Fleet Operations", "description": "Trigger AI-powered fleet optimization", "request": {"name": "optimizationParameters", "displayName": "Optimization Parameters", "schema": {"@type": "Object", "fields": [{"name": "optimizationType", "displayName": "Optimization Type", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Cost", "enumValue": "cost"}, {"name": "Efficiency", "enumValue": "efficiency"}, {"name": "Environmental", "enumValue": "environmental"}, {"name": "Balanced", "enumValue": "balanced"}]}}, {"name": "timeHorizon", "displayName": "Time Horizon (hours)", "schema": "integer"}]}}, "response": {"name": "optimizationResult", "displayName": "Optimization Result", "schema": {"@type": "Object", "fields": [{"name": "success", "schema": "boolean"}, {"name": "recommendations", "schema": "string"}, {"name": "estimatedSavings", "schema": "double"}]}}}, {"@type": "Command", "name": "generateFleetReport", "displayName": "Generate Fleet Report", "description": "Generate comprehensive fleet analytics report using AI", "request": {"name": "reportParameters", "schema": {"@type": "Object", "fields": [{"name": "reportType", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Performance", "enumValue": "performance"}, {"name": "Maintenance", "enumValue": "maintenance"}, {"name": "Environmental", "enumValue": "environmental"}, {"name": "Financial", "enumValue": "financial"}]}}, {"name": "timeRange", "schema": "string"}]}}}, {"@type": "Relationship", "name": "hasVehicles", "displayName": "Has Vehicles", "target": "dtmi:fleetmanagement:Vehicle;1"}, {"@type": "Relationship", "name": "hasDrivers", "displayName": "Has Drivers", "target": "dtmi:fleetmanagement:Driver;1"}, {"@type": "Relationship", "name": "hasDepots", "displayName": "Has Depots", "target": "dtmi:fleetmanagement:Depot;1"}, {"@type": "Relationship", "name": "hasChargingStations", "displayName": "Has Charging Stations", "target": "dtmi:fleetmanagement:ChargingStation;1"}, {"@type": "Relationship", "name": "hasAnalytics", "displayName": "Has Analytics", "target": "dtmi:fleetmanagement:FleetAnalytics;1"}]}