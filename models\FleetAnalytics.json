{"@context": "dtmi:dtdl:context;3", "@id": "dtmi:fleetmanagement:FleetAnalytics;1", "@type": "Interface", "displayName": "Fleet Analytics Engine", "description": "Comprehensive AI-powered fleet analytics with predictive insights and performance optimization", "contents": [{"@type": "Property", "name": "analyticsId", "displayName": "Analytics Engine ID", "description": "Unique identifier for the analytics engine", "schema": "string"}, {"@type": "Property", "name": "analyticsVersion", "displayName": "Analytics Version", "description": "Version of the analytics engine", "schema": "string"}, {"@type": "Property", "name": "dataRetentionPeriod", "displayName": "Data Retention Period (days)", "description": "How long historical data is retained", "schema": "integer"}, {"@type": "Property", "name": "analyticsModules", "displayName": "Active Analytics Modules", "description": "List of active analytics modules", "schema": {"@type": "Array", "elementSchema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "PerformanceAnalytics", "enumValue": "performance"}, {"name": "CostAnalytics", "enumValue": "cost"}, {"name": "SafetyAnalytics", "enumValue": "safety"}, {"name": "EnvironmentalAnalytics", "enumValue": "environmental"}, {"name": "PredictiveAnalytics", "enumValue": "predictive"}, {"name": "DriverAnalytics", "enumValue": "driver"}, {"name": "RouteAnalytics", "enumValue": "route"}, {"name": "MaintenanceAnalytics", "enumValue": "maintenance"}]}}}, {"@type": "Telemetry", "name": "fleetKPIs", "displayName": "Fleet Key Performance Indicators", "description": "Real-time fleet performance metrics and KPIs", "schema": {"@type": "Object", "fields": [{"name": "fleetUtilization", "displayName": "Fleet Utilization Rate (%)", "schema": "double"}, {"name": "averageVehicleAge", "displayName": "Average Vehicle Age (years)", "schema": "double"}, {"name": "totalMileage", "displayName": "Total Fleet Mileage", "schema": "double"}, {"name": "fuelEfficiency", "displayName": "Average Fuel Efficiency (L/100km)", "schema": "double"}, {"name": "energyEfficiency", "displayName": "Average Energy Efficiency (kWh/100km)", "schema": "double"}, {"name": "maintenanceCostPerKm", "displayName": "Maintenance Cost per Km", "schema": "double"}, {"name": "operationalCostPerKm", "displayName": "Operational Cost per Km", "schema": "double"}, {"name": "vehicleDowntime", "displayName": "Average Vehicle Downtime (%)", "schema": "double"}, {"name": "safetyScore", "displayName": "Fleet Safety Score (0-100)", "schema": "double"}, {"name": "environmentalScore", "displayName": "Environmental Impact Score (0-100)", "schema": "double"}, {"name": "driverSatisfactionScore", "displayName": "Driver Satisfaction Score (0-100)", "schema": "double"}, {"name": "customerSatisfactionScore", "displayName": "Customer Satisfaction Score (0-100)", "schema": "double"}]}}, {"@type": "Telemetry", "name": "costAnalytics", "displayName": "Fleet Cost Analytics", "description": "Comprehensive cost analysis and breakdown", "schema": {"@type": "Object", "fields": [{"name": "totalOperationalCost", "displayName": "Total Operational Cost", "schema": "double"}, {"name": "fuelCosts", "displayName": "Fuel Costs", "schema": "double"}, {"name": "energyCosts", "displayName": "Energy/Charging Costs", "schema": "double"}, {"name": "maintenanceCosts", "displayName": "Maintenance Costs", "schema": "double"}, {"name": "insuranceCosts", "displayName": "Insurance Costs", "schema": "double"}, {"name": "driverCosts", "displayName": "Driver-related Costs", "schema": "double"}, {"name": "depreciationCosts", "displayName": "Vehicle Depreciation", "schema": "double"}, {"name": "costPerMile", "displayName": "Cost per Mile", "schema": "double"}, {"name": "costPerVehicle", "displayName": "Average Cost per Vehicle", "schema": "double"}, {"name": "costTrend", "displayName": "Cost Trend (%)", "schema": "double"}, {"name": "budgetVariance", "displayName": "Budget Variance (%)", "schema": "double"}, {"name": "costOptimizationPotential", "displayName": "Cost Optimization Potential ($)", "schema": "double"}]}}, {"@type": "Telemetry", "name": "environmentalMetrics", "displayName": "Environmental Impact Metrics", "description": "Environmental impact analysis and sustainability metrics", "schema": {"@type": "Object", "fields": [{"name": "totalCO2Emissions", "displayName": "Total CO2 Emissions (kg)", "schema": "double"}, {"name": "co2PerKm", "displayName": "CO2 Emissions per Km (g/km)", "schema": "double"}, {"name": "fuelConsumption", "displayName": "Total Fuel Consumption (L)", "schema": "double"}, {"name": "energyConsumption", "displayName": "Total Energy Consumption (kWh)", "schema": "double"}, {"name": "renewableEnergyUsage", "displayName": "Renewable Energy Usage (%)", "schema": "double"}, {"name": "carbonFootprintReduction", "displayName": "Carbon Footprint Reduction (%)", "schema": "double"}, {"name": "sustainabilityScore", "displayName": "Sustainability Score (0-100)", "schema": "double"}, {"name": "greenVehiclePercentage", "displayName": "Green Vehicle Percentage (%)", "schema": "double"}, {"name": "emissionsTrend", "displayName": "Emissions Trend (%)", "schema": "double"}, {"name": "environmentalCompliance", "displayName": "Environmental Compliance Score (%)", "schema": "double"}]}}, {"@type": "Telemetry", "name": "predictiveInsights", "displayName": "Predictive Analytics Insights", "description": "AI-generated predictive insights and forecasts", "schema": {"@type": "Object", "fields": [{"name": "predictedMaintenanceEvents", "displayName": "Predicted Maintenance Events (next 30 days)", "schema": "integer"}, {"name": "predictedDowntime", "displayName": "Predicted Downtime (hours)", "schema": "double"}, {"name": "costForecast", "displayName": "Cost Forecast (next quarter)", "schema": "double"}, {"name": "utilizationForecast", "displayName": "Utilization Forecast (%)", "schema": "double"}, {"name": "riskAssessment", "displayName": "Fleet Risk Assessment", "schema": {"@type": "Object", "fields": [{"name": "overallRiskScore", "schema": "double"}, {"name": "safetyRisk", "schema": "double"}, {"name": "operationalRisk", "schema": "double"}, {"name": "financialRisk", "schema": "double"}, {"name": "complianceRisk", "schema": "double"}]}}, {"name": "optimizationRecommendations", "displayName": "AI Optimization Recommendations", "schema": {"@type": "Array", "elementSchema": {"@type": "Object", "fields": [{"name": "category", "schema": "string"}, {"name": "recommendation", "schema": "string"}, {"name": "potentialSavings", "schema": "double"}, {"name": "implementationEffort", "schema": "string"}, {"name": "priority", "schema": "string"}]}}}]}}, {"@type": "Telemetry", "name": "benchmarkingData", "displayName": "Industry Benchmarking", "description": "Fleet performance compared to industry benchmarks", "schema": {"@type": "Object", "fields": [{"name": "industryAverageFuelEfficiency", "displayName": "Industry Average Fuel Efficiency", "schema": "double"}, {"name": "fuelEfficiencyPercentile", "displayName": "Fuel Efficiency Percentile", "schema": "double"}, {"name": "industryAverageMaintenanceCost", "displayName": "Industry Average Maintenance Cost", "schema": "double"}, {"name": "maintenanceCostPercentile", "displayName": "Maintenance Cost Percentile", "schema": "double"}, {"name": "industryAverageSafetyScore", "displayName": "Industry Average Safety Score", "schema": "double"}, {"name": "safetyScorePercentile", "displayName": "Safety Score Percentile", "schema": "double"}, {"name": "competitivePosition", "displayName": "Competitive Position", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "TopPerformer", "enumValue": "top_performer"}, {"name": "AboveAverage", "enumValue": "above_average"}, {"name": "Average", "enumValue": "average"}, {"name": "BelowAverage", "enumValue": "below_average"}, {"name": "NeedsImprovement", "enumValue": "needs_improvement"}]}}]}}, {"@type": "Command", "name": "generateAnalyticsReport", "displayName": "Generate Analytics Report", "description": "Generate comprehensive fleet analytics report with AI insights", "request": {"name": "reportRequest", "schema": {"@type": "Object", "fields": [{"name": "reportType", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Executive", "enumValue": "executive"}, {"name": "Operational", "enumValue": "operational"}, {"name": "Financial", "enumValue": "financial"}, {"name": "Environmental", "enumValue": "environmental"}, {"name": "Safety", "enumValue": "safety"}, {"name": "Predictive", "enumValue": "predictive"}, {"name": "Comprehensive", "enumValue": "comprehensive"}]}}, {"name": "timeRange", "schema": {"@type": "Object", "fields": [{"name": "startDate", "schema": "dateTime"}, {"name": "endDate", "schema": "dateTime"}]}}, {"name": "includeComparisons", "displayName": "Include Historical Comparisons", "schema": "boolean"}, {"name": "includeBenchmarks", "displayName": "Include Industry Benchmarks", "schema": "boolean"}, {"name": "includeRecommendations", "displayName": "Include AI Recommendations", "schema": "boolean"}, {"name": "vehicleFilter", "displayName": "Vehicle Filter (optional)", "schema": {"@type": "Array", "elementSchema": "string"}}]}}, "response": {"name": "analyticsReport", "schema": {"@type": "Object", "fields": [{"name": "reportId", "schema": "string"}, {"name": "generationTimestamp", "schema": "dateTime"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "schema": "string"}, {"name": "keyFindings", "schema": {"@type": "Array", "elementSchema": "string"}}, {"name": "recommendations", "schema": {"@type": "Array", "elementSchema": "string"}}, {"name": "reportUrl", "schema": "string"}]}}}, {"@type": "Command", "name": "runPredictiveAnalysis", "displayName": "Run Predictive Analysis", "description": "Execute AI-powered predictive analysis for fleet optimization", "request": {"name": "predictiveAnalysisRequest", "schema": {"@type": "Object", "fields": [{"name": "analysisType", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "MaintenancePrediction", "enumValue": "maintenance_prediction"}, {"name": "CostForecasting", "enumValue": "cost_forecasting"}, {"name": "UtilizationOptimization", "enumValue": "utilization_optimization"}, {"name": "RiskAssessment", "enumValue": "risk_assessment"}, {"name": "PerformancePrediction", "enumValue": "performance_prediction"}]}}, {"name": "timeHorizon", "displayName": "Prediction Time Horizon (days)", "schema": "integer"}, {"name": "confidenceLevel", "displayName": "Required Confidence Level (%)", "schema": "double"}, {"name": "includeScenarioAnalysis", "schema": "boolean"}]}}, "response": {"name": "predictiveAnalysisResult", "schema": {"@type": "Object", "fields": [{"name": "analysisId", "schema": "string"}, {"name": "predictions", "schema": {"@type": "Array", "elementSchema": {"@type": "Object", "fields": [{"name": "metric", "schema": "string"}, {"name": "predictedValue", "schema": "double"}, {"name": "confidence", "schema": "double"}, {"name": "timeframe", "schema": "string"}]}}}, {"name": "riskFactors", "schema": {"@type": "Array", "elementSchema": "string"}}, {"name": "actionableInsights", "schema": {"@type": "Array", "elementSchema": "string"}}]}}}, {"@type": "Command", "name": "optimizeFleetConfiguration", "displayName": "Optimize Fleet Configuration", "description": "AI-powered fleet configuration optimization recommendations", "request": {"name": "optimizationRequest", "schema": {"@type": "Object", "fields": [{"name": "optimizationGoals", "schema": {"@type": "Array", "elementSchema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "CostReduction", "enumValue": "cost_reduction"}, {"name": "EfficiencyImprovement", "enumValue": "efficiency_improvement"}, {"name": "EmissionReduction", "enumValue": "emission_reduction"}, {"name": "SafetyImprovement", "enumValue": "safety_improvement"}, {"name": "UtilizationOptimization", "enumValue": "utilization_optimization"}]}}}, {"name": "constraints", "schema": {"@type": "Object", "fields": [{"name": "budgetLimit", "schema": "double"}, {"name": "timeframe", "schema": "integer"}, {"name": "operationalRequirements", "schema": "string"}]}}, {"name": "includeVehicleReplacement", "schema": "boolean"}, {"name": "includeRouteOptimization", "schema": "boolean"}]}}, "response": {"name": "optimizationRecommendations", "schema": {"@type": "Object", "fields": [{"name": "recommendationId", "schema": "string"}, {"name": "recommendations", "schema": {"@type": "Array", "elementSchema": {"@type": "Object", "fields": [{"name": "category", "schema": "string"}, {"name": "action", "schema": "string"}, {"name": "expectedBenefit", "schema": "string"}, {"name": "implementationCost", "schema": "double"}, {"name": "paybackPeriod", "schema": "double"}, {"name": "priority", "schema": "string"}]}}}, {"name": "totalPotentialSavings", "schema": "double"}, {"name": "implementationTimeline", "schema": "string"}]}}}, {"@type": "Relationship", "name": "analyzedFleet", "displayName": "Analyzed Fleet", "target": "dtmi:fleetmanagement:Fleet;1"}, {"@type": "Relationship", "name": "dataConnections", "displayName": "Data Source Connections", "target": "dtmi:fleetmanagement:DataConnector;1"}]}