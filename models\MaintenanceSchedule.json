{"@context": "dtmi:dtdl:context;3", "@id": "dtmi:fleetmanagement:MaintenanceSchedule;1", "@type": "Interface", "displayName": "Maintenance Schedule", "description": "AI-optimized maintenance scheduling with predictive maintenance integration", "contents": [{"@type": "Property", "name": "scheduleId", "displayName": "Schedule ID", "description": "Unique identifier for the maintenance schedule", "schema": "string"}, {"@type": "Property", "name": "vehicleId", "displayName": "Vehicle ID", "description": "ID of the vehicle this schedule applies to", "schema": "string"}, {"@type": "Property", "name": "scheduleType", "displayName": "Schedule Type", "description": "Type of maintenance schedule", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Preventive", "displayName": "Preventive Maintenance", "enumValue": "preventive"}, {"name": "Predictive", "displayName": "Predictive Maintenance", "enumValue": "predictive"}, {"name": "Corrective", "displayName": "Corrective Maintenance", "enumValue": "corrective"}, {"name": "Emergency", "displayName": "Emergency Maintenance", "enumValue": "emergency"}, {"name": "Routine", "displayName": "Routine Service", "enumValue": "routine"}]}}, {"@type": "Property", "name": "maintenanceItems", "displayName": "Maintenance Items", "description": "List of maintenance tasks to be performed", "schema": {"@type": "Array", "elementSchema": {"@type": "Object", "fields": [{"name": "itemId", "displayName": "Item ID", "schema": "string"}, {"name": "description", "displayName": "Task Description", "schema": "string"}, {"name": "category", "displayName": "Maintenance Category", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Engine", "enumValue": "engine"}, {"name": "Transmission", "enumValue": "transmission"}, {"name": "<PERSON>rakes", "enumValue": "brakes"}, {"name": "Tires", "enumValue": "tires"}, {"name": "Electrical", "enumValue": "electrical"}, {"name": "Battery", "enumValue": "battery"}, {"name": "HVAC", "enumValue": "hvac"}, {"name": "Safety", "enumValue": "safety"}, {"name": "Bodywork", "enumValue": "bodywork"}]}}, {"name": "priority", "displayName": "Priority Level", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Low", "enumValue": "low"}, {"name": "Medium", "enumValue": "medium"}, {"name": "High", "enumValue": "high"}, {"name": "Critical", "enumValue": "critical"}]}}, {"name": "estimatedDuration", "displayName": "Estimated Duration (hours)", "schema": "double"}, {"name": "estimatedCost", "displayName": "Estimated Cost", "schema": "double"}, {"name": "requiredParts", "displayName": "Required Parts", "schema": {"@type": "Array", "elementSchema": {"@type": "Object", "fields": [{"name": "partNumber", "schema": "string"}, {"name": "partName", "schema": "string"}, {"name": "quantity", "schema": "integer"}, {"name": "cost", "schema": "double"}]}}}, {"name": "skillsRequired", "displayName": "Required Skills", "schema": {"@type": "Array", "elementSchema": "string"}}]}}}, {"@type": "Property", "name": "schedulingConstraints", "displayName": "Scheduling Constraints", "description": "Constraints for scheduling maintenance", "schema": {"@type": "Object", "fields": [{"name": "maxDowntime", "displayName": "Maximum Downtime (hours)", "schema": "double"}, {"name": "preferredTimeWindows", "displayName": "Preferred Time Windows", "schema": {"@type": "Array", "elementSchema": {"@type": "Object", "fields": [{"name": "startTime", "schema": "time"}, {"name": "endTime", "schema": "time"}, {"name": "daysOfWeek", "schema": {"@type": "Array", "elementSchema": "string"}}]}}}, {"name": "blackoutPeriods", "displayName": "Blackout Periods", "schema": {"@type": "Array", "elementSchema": {"@type": "Object", "fields": [{"name": "startDate", "schema": "dateTime"}, {"name": "endDate", "schema": "dateTime"}, {"name": "reason", "schema": "string"}]}}}, {"name": "requiredFacilities", "displayName": "Required Facilities", "schema": {"@type": "Array", "elementSchema": "string"}}]}}, {"@type": "Telemetry", "name": "scheduleStatus", "displayName": "Schedule Status", "description": "Current status and progress of the maintenance schedule", "schema": {"@type": "Object", "fields": [{"name": "currentStatus", "displayName": "Current Status", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Planned", "enumValue": "planned"}, {"name": "Scheduled", "enumValue": "scheduled"}, {"name": "InProgress", "enumValue": "in_progress"}, {"name": "Completed", "enumValue": "completed"}, {"name": "Cancelled", "enumValue": "cancelled"}, {"name": "Delayed", "enumValue": "delayed"}, {"name": "OnHold", "enumValue": "on_hold"}]}}, {"name": "scheduledStartTime", "displayName": "Scheduled Start Time", "schema": "dateTime"}, {"name": "scheduledEndTime", "displayName": "Scheduled End Time", "schema": "dateTime"}, {"name": "actualStartTime", "displayName": "Actual Start Time", "schema": "dateTime"}, {"name": "actualEndTime", "displayName": "Actual End Time", "schema": "dateTime"}, {"name": "completionPercentage", "displayName": "Completion Percentage", "schema": "double"}, {"name": "assignedTechnicians", "displayName": "Assigned Technicians", "schema": {"@type": "Array", "elementSchema": {"@type": "Object", "fields": [{"name": "technicianId", "schema": "string"}, {"name": "name", "schema": "string"}, {"name": "specialization", "schema": "string"}, {"name": "assignedTasks", "schema": {"@type": "Array", "elementSchema": "string"}}]}}}, {"name": "assignedBay", "displayName": "Assigned Maintenance Bay", "schema": "string"}, {"name": "partsAvailability", "displayName": "Parts Availability Status", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Available", "enumValue": "available"}, {"name": "PartiallyAvailable", "enumValue": "partially_available"}, {"name": "Ordered", "enumValue": "ordered"}, {"name": "BackOrdered", "enumValue": "back_ordered"}, {"name": "NotAvailable", "enumValue": "not_available"}]}}]}}, {"@type": "Telemetry", "name": "maintenanceMetrics", "displayName": "Maintenance Performance Metrics", "description": "Performance metrics for maintenance execution", "schema": {"@type": "Object", "fields": [{"name": "actualDuration", "displayName": "Actual Duration (hours)", "schema": "double"}, {"name": "actualCost", "displayName": "Actual Cost", "schema": "double"}, {"name": "laborCost", "displayName": "Labor Cost", "schema": "double"}, {"name": "partsCost", "displayName": "Parts Cost", "schema": "double"}, {"name": "overheadCost", "displayName": "Overhead Cost", "schema": "double"}, {"name": "scheduleVariance", "displayName": "Schedule Variance (hours)", "schema": "double"}, {"name": "costVariance", "displayName": "Cost <PERSON><PERSON>", "schema": "double"}, {"name": "qualityScore", "displayName": "Maintenance Quality Score (0-100)", "schema": "double"}, {"name": "firstTimeFixRate", "displayName": "First Time Fix Rate (%)", "schema": "double"}, {"name": "customerSatisfaction", "displayName": "Customer Satisfaction Score (0-100)", "schema": "double"}]}}, {"@type": "Telemetry", "name": "predictiveInsights", "displayName": "Predictive Maintenance Insights", "description": "AI-generated insights for predictive maintenance", "schema": {"@type": "Object", "fields": [{"name": "failureProbability", "displayName": "Component Failure Probability (%)", "schema": "double"}, {"name": "remainingUsefulLife", "displayName": "Remaining Useful Life (days)", "schema": "integer"}, {"name": "maintenanceUrgency", "displayName": "Maintenance Urgency Score (0-100)", "schema": "double"}, {"name": "costBenefitAnalysis", "displayName": "Cost-Benefit Analysis", "schema": {"@type": "Object", "fields": [{"name": "preventiveCost", "schema": "double"}, {"name": "correctiveCost", "schema": "double"}, {"name": "downtimeCost", "schema": "double"}, {"name": "recommendedAction", "schema": "string"}]}}, {"name": "riskFactors", "displayName": "Risk Factors", "schema": {"@type": "Array", "elementSchema": {"@type": "Object", "fields": [{"name": "factor", "schema": "string"}, {"name": "impact", "schema": "double"}, {"name": "mitigation", "schema": "string"}]}}}]}}, {"@type": "Command", "name": "optimizeSchedule", "displayName": "Optimize Maintenance Schedule", "description": "AI-powered optimization of maintenance scheduling", "request": {"name": "optimizationRequest", "schema": {"@type": "Object", "fields": [{"name": "optimizationGoals", "schema": {"@type": "Array", "elementSchema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "MinimizeDowntime", "enumValue": "minimize_downtime"}, {"name": "MinimizeCost", "enumValue": "minimize_cost"}, {"name": "MaximizeReliability", "enumValue": "maximize_reliability"}, {"name": "BalanceWorkload", "enumValue": "balance_workload"}, {"name": "OptimizeResourceUtilization", "enumValue": "optimize_resource_utilization"}]}}}, {"name": "timeHorizon", "displayName": "Optimization Time Horizon (days)", "schema": "integer"}, {"name": "resourceConstraints", "schema": {"@type": "Object", "fields": [{"name": "availableTechnicians", "schema": "integer"}, {"name": "availableBays", "schema": "integer"}, {"name": "budgetLimit", "schema": "double"}]}}, {"name": "includePredictiveData", "schema": "boolean"}]}}, "response": {"name": "optimizedSchedule", "schema": {"@type": "Object", "fields": [{"name": "optimizationId", "schema": "string"}, {"name": "newSchedule", "schema": {"@type": "Array", "elementSchema": {"@type": "Object", "fields": [{"name": "vehicleId", "schema": "string"}, {"name": "scheduledTime", "schema": "dateTime"}, {"name": "maintenanceType", "schema": "string"}, {"name": "priority", "schema": "string"}]}}}, {"name": "expectedBenefits", "schema": {"@type": "Object", "fields": [{"name": "downtimeReduction", "schema": "double"}, {"name": "costSavings", "schema": "double"}, {"name": "reliabilityImprovement", "schema": "double"}]}}]}}}, {"@type": "Command", "name": "updateSchedule", "displayName": "Update Maintenance Schedule", "description": "Update maintenance schedule with new information or changes", "request": {"name": "scheduleUpdate", "schema": {"@type": "Object", "fields": [{"name": "updateType", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Reschedule", "enumValue": "reschedule"}, {"name": "AddTask", "enumValue": "add_task"}, {"name": "RemoveTask", "enumValue": "remove_task"}, {"name": "ChangePriority", "enumValue": "change_priority"}, {"name": "UpdateResources", "enumValue": "update_resources"}]}}, {"name": "newScheduledTime", "schema": "dateTime"}, {"name": "reason", "schema": "string"}, {"name": "impactAssessment", "schema": "boolean"}]}}}, {"@type": "Command", "name": "generateMaintenanceReport", "displayName": "Generate Maintenance Report", "description": "Generate comprehensive maintenance performance report", "request": {"name": "reportRequest", "schema": {"@type": "Object", "fields": [{"name": "reportType", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Performance", "enumValue": "performance"}, {"name": "Cost", "enumValue": "cost"}, {"name": "Efficiency", "enumValue": "efficiency"}, {"name": "Predictive", "enumValue": "predictive"}, {"name": "Comprehensive", "enumValue": "comprehensive"}]}}, {"name": "timeRange", "schema": {"@type": "Object", "fields": [{"name": "startDate", "schema": "dateTime"}, {"name": "endDate", "schema": "dateTime"}]}}, {"name": "includeRecommendations", "schema": "boolean"}]}}}, {"@type": "Relationship", "name": "scheduledVehicle", "displayName": "Scheduled Vehicle", "target": "dtmi:fleetmanagement:Vehicle;1"}, {"@type": "Relationship", "name": "assignedDepot", "displayName": "Assigned Depot", "target": "dtmi:fleetmanagement:Depot;1"}, {"@type": "Relationship", "name": "predictiveModel", "displayName": "Predictive Maintenance Model", "target": "dtmi:fleetmanagement:PredictiveMaintenanceModel;1"}]}