{"@context": "dtmi:dtdl:context;3", "@id": "dtmi:fleetmanagement:PredictiveMaintenanceModel;1", "@type": "Interface", "displayName": "Predictive Maintenance AI Model", "description": "AI-powered predictive maintenance model for fleet vehicles with machine learning capabilities", "contents": [{"@type": "Property", "name": "modelId", "displayName": "Model ID", "description": "Unique identifier for the predictive maintenance model", "schema": "string"}, {"@type": "Property", "name": "modelName", "displayName": "Model Name", "description": "Name of the predictive maintenance model", "schema": "string"}, {"@type": "Property", "name": "modelVersion", "displayName": "Model Version", "description": "Version of the AI model", "schema": "string"}, {"@type": "Property", "name": "modelType", "displayName": "Model Type", "description": "Type of machine learning model", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "RandomForest", "enumValue": "random_forest"}, {"name": "NeuralNetwork", "enumValue": "neural_network"}, {"name": "SVM", "enumValue": "svm"}, {"name": "GradientBoosting", "enumValue": "gradient_boosting"}, {"name": "LSTM", "enumValue": "lstm"}, {"name": "Ensemble", "enumValue": "ensemble"}]}}, {"@type": "Property", "name": "trainingDataSize", "displayName": "Training Data Size", "description": "Number of records used for training", "schema": "integer"}, {"@type": "Property", "name": "lastTrainingDate", "displayName": "Last Training Date", "description": "Date when the model was last trained", "schema": "dateTime"}, {"@type": "Property", "name": "modelAccuracy", "displayName": "Model Accuracy (%)", "description": "Current model accuracy percentage", "schema": "double"}, {"@type": "Property", "name": "supportedVehicleTypes", "displayName": "Supported Vehicle Types", "description": "Types of vehicles this model supports", "schema": {"@type": "Array", "elementSchema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "ICE", "enumValue": "ice"}, {"name": "Electric", "enumValue": "electric"}, {"name": "Hybrid", "enumValue": "hybrid"}, {"name": "PluginHybrid", "enumValue": "plugin_hybrid"}]}}}, {"@type": "Telemetry", "name": "modelPerformance", "displayName": "Model Performance Metrics", "description": "Real-time model performance and accuracy metrics", "schema": {"@type": "Object", "fields": [{"name": "precision", "displayName": "Precision", "schema": "double"}, {"name": "recall", "displayName": "Recall", "schema": "double"}, {"name": "f1Score", "displayName": "F1 Score", "schema": "double"}, {"name": "auc", "displayName": "Area Under Curve", "schema": "double"}, {"name": "falsePositiveRate", "displayName": "False Positive Rate", "schema": "double"}, {"name": "falseNegativeRate", "displayName": "False Negative Rate", "schema": "double"}, {"name": "predictionLatency", "displayName": "Prediction Latency (ms)", "schema": "double"}, {"name": "throughput", "displayName": "Predictions per Second", "schema": "double"}]}}, {"@type": "Telemetry", "name": "predictionResults", "displayName": "Maintenance Prediction Results", "description": "Current maintenance predictions for monitored vehicles", "schema": {"@type": "Object", "fields": [{"name": "vehicleId", "displayName": "Vehicle ID", "schema": "string"}, {"name": "maintenanceType", "displayName": "Predicted Maintenance Type", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Engine", "enumValue": "engine"}, {"name": "Transmission", "enumValue": "transmission"}, {"name": "<PERSON>rakes", "enumValue": "brakes"}, {"name": "Tires", "enumValue": "tires"}, {"name": "Battery", "enumValue": "battery"}, {"name": "Electrical", "enumValue": "electrical"}, {"name": "Cooling", "enumValue": "cooling"}, {"name": "Suspension", "enumValue": "suspension"}]}}, {"name": "failureProbability", "displayName": "Failure Probability (%)", "schema": "double"}, {"name": "predictedFailureDate", "displayName": "Predicted Failure Date", "schema": "dateTime"}, {"name": "remainingUsefulLife", "displayName": "Remaining Useful Life (days)", "schema": "integer"}, {"name": "confidenceLevel", "displayName": "Prediction Confidence (%)", "schema": "double"}, {"name": "recommendedAction", "displayName": "Recommended Action", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Monitor", "enumValue": "monitor"}, {"name": "ScheduleMaintenance", "enumValue": "schedule_maintenance"}, {"name": "ImmediateMaintenance", "enumValue": "immediate_maintenance"}, {"name": "ReplaceComponent", "enumValue": "replace_component"}, {"name": "TakeOutOfService", "enumValue": "take_out_of_service"}]}}, {"name": "estimatedCost", "displayName": "Estimated Maintenance Cost", "schema": "double"}, {"name": "criticalityScore", "displayName": "Criticality Score (0-100)", "schema": "double"}]}}, {"@type": "Telemetry", "name": "featureImportance", "displayName": "Feature Importance Analysis", "description": "Analysis of which vehicle parameters are most predictive of maintenance needs", "schema": {"@type": "Array", "elementSchema": {"@type": "Object", "fields": [{"name": "featureName", "displayName": "Feature Name", "schema": "string"}, {"name": "importance", "displayName": "Importance Score (0-1)", "schema": "double"}, {"name": "category", "displayName": "Feature Category", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Engine", "enumValue": "engine"}, {"name": "Drivetrain", "enumValue": "drivetrain"}, {"name": "Electrical", "enumValue": "electrical"}, {"name": "Usage", "enumValue": "usage"}, {"name": "Environmental", "enumValue": "environmental"}, {"name": "Driver", "enumValue": "driver"}]}}]}}}, {"@type": "Command", "name": "predictMaintenance", "displayName": "Predict Maintenance Needs", "description": "Generate maintenance predictions for a specific vehicle or fleet", "request": {"name": "predictionRequest", "schema": {"@type": "Object", "fields": [{"name": "vehicleId", "displayName": "Vehicle ID (optional for fleet-wide prediction)", "schema": "string"}, {"name": "predictionHorizon", "displayName": "Prediction Horizon (days)", "schema": "integer"}, {"name": "includeUncertainty", "displayName": "Include Uncertainty Analysis", "schema": "boolean"}, {"name": "maintenanceTypes", "displayName": "Specific Maintenance Types to Predict", "schema": {"@type": "Array", "elementSchema": "string"}}]}}, "response": {"name": "maintenancePrediction", "schema": {"@type": "Object", "fields": [{"name": "predictionId", "schema": "string"}, {"name": "predictions", "schema": {"@type": "Array", "elementSchema": {"@type": "Object", "fields": [{"name": "vehicleId", "schema": "string"}, {"name": "component", "schema": "string"}, {"name": "failureProbability", "schema": "double"}, {"name": "timeToFailure", "schema": "integer"}, {"name": "confidence", "schema": "double"}]}}}, {"name": "recommendedActions", "schema": {"@type": "Array", "elementSchema": "string"}}]}}}, {"@type": "Command", "name": "retrainModel", "displayName": "Retrain Model", "description": "Retrain the predictive maintenance model with new data", "request": {"name": "retrainingRequest", "schema": {"@type": "Object", "fields": [{"name": "includeRecentData", "displayName": "Include Recent Data", "schema": "boolean"}, {"name": "dataStartDate", "displayName": "Training Data Start Date", "schema": "dateTime"}, {"name": "dataEndDate", "displayName": "Training Data End Date", "schema": "dateTime"}, {"name": "hyperparameterTuning", "displayName": "Perform Hyperparameter Tuning", "schema": "boolean"}, {"name": "validationSplit", "displayName": "Validation Split Ratio", "schema": "double"}]}}, "response": {"name": "retrainingResult", "schema": {"@type": "Object", "fields": [{"name": "success", "schema": "boolean"}, {"name": "newModelVersion", "schema": "string"}, {"name": "accuracyImprovement", "schema": "double"}, {"name": "trainingDuration", "schema": "double"}, {"name": "modelMetrics", "schema": {"@type": "Object", "fields": [{"name": "accuracy", "schema": "double"}, {"name": "precision", "schema": "double"}, {"name": "recall", "schema": "double"}, {"name": "f1Score", "schema": "double"}]}}]}}}, {"@type": "Command", "name": "explainPrediction", "displayName": "Explain Prediction", "description": "Provide explainable AI insights for a specific maintenance prediction", "request": {"name": "explanationRequest", "schema": {"@type": "Object", "fields": [{"name": "vehicleId", "schema": "string"}, {"name": "predictionId", "schema": "string"}, {"name": "explanationType", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "FeatureImportance", "enumValue": "feature_importance"}, {"name": "SHAP", "enumValue": "shap"}, {"name": "LIME", "enumValue": "lime"}, {"name": "CounterfactualAnalysis", "enumValue": "counterfactual"}]}}]}}, "response": {"name": "predictionExplanation", "schema": {"@type": "Object", "fields": [{"name": "explanation", "schema": "string"}, {"name": "keyFactors", "schema": {"@type": "Array", "elementSchema": {"@type": "Object", "fields": [{"name": "factor", "schema": "string"}, {"name": "impact", "schema": "double"}, {"name": "direction", "schema": "string"}]}}}, {"name": "recommendations", "schema": {"@type": "Array", "elementSchema": "string"}}]}}}, {"@type": "Command", "name": "optimizeMaintenanceSchedule", "displayName": "Optimize Maintenance Schedule", "description": "Generate optimized maintenance schedule based on predictions and constraints", "request": {"name": "scheduleOptimizationRequest", "schema": {"@type": "Object", "fields": [{"name": "fleetId", "schema": "string"}, {"name": "timeHorizon", "displayName": "Schedule Time Horizon (days)", "schema": "integer"}, {"name": "maintenanceCapacity", "displayName": "Daily Maintenance Capacity", "schema": "integer"}, {"name": "priorityWeights", "schema": {"@type": "Object", "fields": [{"name": "safety", "schema": "double"}, {"name": "cost", "schema": "double"}, {"name": "availability", "schema": "double"}]}}]}}, "response": {"name": "optimizedSchedule", "schema": {"@type": "Object", "fields": [{"name": "scheduleId", "schema": "string"}, {"name": "maintenanceEvents", "schema": {"@type": "Array", "elementSchema": {"@type": "Object", "fields": [{"name": "vehicleId", "schema": "string"}, {"name": "scheduledDate", "schema": "dateTime"}, {"name": "maintenanceType", "schema": "string"}, {"name": "estimatedDuration", "schema": "double"}, {"name": "priority", "schema": "string"}]}}}, {"name": "totalCost", "schema": "double"}, {"name": "fleetAvailability", "schema": "double"}]}}}, {"@type": "Relationship", "name": "monitoredVehicles", "displayName": "Monitored Vehicles", "target": "dtmi:fleetmanagement:Vehicle;1"}, {"@type": "Relationship", "name": "maintenanceSchedules", "displayName": "Generated Maintenance Schedules", "target": "dtmi:fleetmanagement:MaintenanceSchedule;1"}]}