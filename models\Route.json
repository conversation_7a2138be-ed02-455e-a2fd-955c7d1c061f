{"@context": "dtmi:dtdl:context;3", "@id": "dtmi:fleetmanagement:Route;1", "@type": "Interface", "displayName": "Fleet Route", "description": "Route planning and optimization with AI-powered traffic and efficiency analysis", "contents": [{"@type": "Property", "name": "routeId", "displayName": "Route ID", "description": "Unique identifier for the route", "schema": "string"}, {"@type": "Property", "name": "routeName", "displayName": "Route Name", "description": "Descriptive name for the route", "schema": "string"}, {"@type": "Property", "name": "routeType", "displayName": "Route Type", "description": "Type of route operation", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Delivery", "enumValue": "delivery"}, {"name": "Pickup", "enumValue": "pickup"}, {"name": "Service", "enumValue": "service"}, {"name": "Transport", "enumValue": "transport"}, {"name": "Emergency", "enumValue": "emergency"}, {"name": "Maintenance", "enumValue": "maintenance"}]}}, {"@type": "Property", "name": "priority", "displayName": "Route Priority", "description": "Priority level of the route", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Low", "enumValue": "low"}, {"name": "Normal", "enumValue": "normal"}, {"name": "High", "enumValue": "high"}, {"name": "Critical", "enumValue": "critical"}]}}, {"@type": "Property", "name": "startLocation", "displayName": "Start Location", "description": "Route starting point", "schema": {"@type": "Object", "fields": [{"name": "latitude", "displayName": "Latitude", "schema": "double"}, {"name": "longitude", "displayName": "Longitude", "schema": "double"}, {"name": "address", "displayName": "Address", "schema": "string"}, {"name": "locationName", "displayName": "Location Name", "schema": "string"}]}}, {"@type": "Property", "name": "endLocation", "displayName": "End Location", "description": "Route destination", "schema": {"@type": "Object", "fields": [{"name": "latitude", "displayName": "Latitude", "schema": "double"}, {"name": "longitude", "displayName": "Longitude", "schema": "double"}, {"name": "address", "displayName": "Address", "schema": "string"}, {"name": "locationName", "displayName": "Location Name", "schema": "string"}]}}, {"@type": "Property", "name": "waypoints", "displayName": "Route Waypoints", "description": "Intermediate stops along the route", "schema": {"@type": "Array", "elementSchema": {"@type": "Object", "fields": [{"name": "waypointId", "schema": "string"}, {"name": "latitude", "schema": "double"}, {"name": "longitude", "schema": "double"}, {"name": "address", "schema": "string"}, {"name": "stopType", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Delivery", "enumValue": "delivery"}, {"name": "Pickup", "enumValue": "pickup"}, {"name": "Service", "enumValue": "service"}, {"name": "Rest", "enumValue": "rest"}, {"name": "Fuel", "enumValue": "fuel"}, {"name": "Charging", "enumValue": "charging"}]}}, {"name": "estimatedStopDuration", "displayName": "Estimated Stop Duration (minutes)", "schema": "integer"}, {"name": "timeWindow", "displayName": "Time Window", "schema": {"@type": "Object", "fields": [{"name": "earliestArrival", "schema": "dateTime"}, {"name": "latestArrival", "schema": "dateTime"}]}}]}}}, {"@type": "Telemetry", "name": "routeProgress", "displayName": "Route Progress", "description": "Real-time route execution progress", "schema": {"@type": "Object", "fields": [{"name": "currentWaypointIndex", "displayName": "Current Waypoint Index", "schema": "integer"}, {"name": "completedWaypoints", "displayName": "Completed Waypoints", "schema": "integer"}, {"name": "totalWaypoints", "displayName": "Total Waypoints", "schema": "integer"}, {"name": "distanceTraveled", "displayName": "Distance Traveled (km)", "schema": "double"}, {"name": "remainingDistance", "displayName": "Remaining Distance (km)", "schema": "double"}, {"name": "elapsedTime", "displayName": "Elapsed Time (minutes)", "schema": "double"}, {"name": "estimatedTimeRemaining", "displayName": "Estimated Time Remaining (minutes)", "schema": "double"}, {"name": "routeStatus", "displayName": "Route Status", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Planned", "enumValue": "planned"}, {"name": "InProgress", "enumValue": "in_progress"}, {"name": "Delayed", "enumValue": "delayed"}, {"name": "Completed", "enumValue": "completed"}, {"name": "Cancelled", "enumValue": "cancelled"}, {"name": "Rerouted", "enumValue": "rerouted"}]}}]}}, {"@type": "Telemetry", "name": "routeMetrics", "displayName": "Route Performance Metrics", "description": "AI-analyzed route performance and efficiency metrics", "schema": {"@type": "Object", "fields": [{"name": "totalDistance", "displayName": "Total Distance (km)", "schema": "double"}, {"name": "plannedDuration", "displayName": "Planned Duration (minutes)", "schema": "double"}, {"name": "actualDuration", "displayName": "Actual Duration (minutes)", "schema": "double"}, {"name": "fuelConsumption", "displayName": "Fuel Consumption (L)", "schema": "double"}, {"name": "energyConsumption", "displayName": "Energy Consumption (kWh)", "schema": "double"}, {"name": "averageSpeed", "displayName": "Average Speed (km/h)", "schema": "double"}, {"name": "trafficDelayTime", "displayName": "Traffic Delay Time (minutes)", "schema": "double"}, {"name": "weatherImpactScore", "displayName": "Weather Impact Score (0-100)", "schema": "double"}, {"name": "routeEfficiencyScore", "displayName": "AI Route Efficiency Score (0-100)", "schema": "double"}, {"name": "onTimePerformance", "displayName": "On-Time Performance (%)", "schema": "double"}, {"name": "co2Emissions", "displayName": "CO2 Emissions (kg)", "schema": "double"}]}}, {"@type": "Telemetry", "name": "trafficConditions", "displayName": "Real-time Traffic Conditions", "description": "Current traffic and road conditions along the route", "schema": {"@type": "Object", "fields": [{"name": "overallTrafficLevel", "displayName": "Overall Traffic Level", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Light", "enumValue": "light"}, {"name": "Moderate", "enumValue": "moderate"}, {"name": "Heavy", "enumValue": "heavy"}, {"name": "Severe", "enumValue": "severe"}]}}, {"name": "accidents", "displayName": "Reported Accidents", "schema": {"@type": "Array", "elementSchema": {"@type": "Object", "fields": [{"name": "latitude", "schema": "double"}, {"name": "longitude", "schema": "double"}, {"name": "severity", "schema": "string"}, {"name": "estimatedClearTime", "schema": "dateTime"}]}}}, {"name": "roadClosures", "displayName": "Road Closures", "schema": {"@type": "Array", "elementSchema": "string"}}, {"name": "weatherConditions", "displayName": "Weather Conditions", "schema": {"@type": "Object", "fields": [{"name": "condition", "schema": "string"}, {"name": "temperature", "schema": "double"}, {"name": "precipitation", "schema": "double"}, {"name": "windSpeed", "schema": "double"}, {"name": "visibility", "schema": "double"}]}}]}}, {"@type": "Command", "name": "optimizeRoute", "displayName": "Optimize Route", "description": "AI-powered route optimization considering traffic, weather, and vehicle constraints", "request": {"name": "optimizationRequest", "schema": {"@type": "Object", "fields": [{"name": "optimizationGoal", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "MinimizeTime", "enumValue": "minimize_time"}, {"name": "MinimizeDistance", "enumValue": "minimize_distance"}, {"name": "MinimizeFuel", "enumValue": "minimize_fuel"}, {"name": "MinimizeEmissions", "enumValue": "minimize_emissions"}, {"name": "BalancedOptimization", "enumValue": "balanced"}]}}, {"name": "vehicleConstraints", "schema": {"@type": "Object", "fields": [{"name": "vehicleType", "schema": "string"}, {"name": "fuelLevel", "schema": "double"}, {"name": "batteryLevel", "schema": "double"}, {"name": "max<PERSON><PERSON><PERSON>", "schema": "double"}]}}, {"name": "includeRealTimeTraffic", "schema": "boolean"}, {"name": "includeWeatherData", "schema": "boolean"}]}}, "response": {"name": "optimizedRoute", "schema": {"@type": "Object", "fields": [{"name": "newRouteId", "schema": "string"}, {"name": "estimatedTimeSaving", "schema": "double"}, {"name": "estimatedFuelSaving", "schema": "double"}, {"name": "estimatedCostSaving", "schema": "double"}, {"name": "optimizedWaypoints", "schema": {"@type": "Array", "elementSchema": "string"}}, {"name": "alternativeRoutes", "schema": {"@type": "Array", "elementSchema": "string"}}]}}}, {"@type": "Command", "name": "predictArrivalTime", "displayName": "Predict Arrival Time", "description": "AI-powered arrival time prediction with confidence intervals", "request": {"name": "predictionRequest", "schema": {"@type": "Object", "fields": [{"name": "waypointId", "schema": "string"}, {"name": "includeTrafficPrediction", "schema": "boolean"}, {"name": "includeWeatherForecast", "schema": "boolean"}]}}, "response": {"name": "arrivalPrediction", "schema": {"@type": "Object", "fields": [{"name": "estimatedArrivalTime", "schema": "dateTime"}, {"name": "confidenceInterval", "schema": {"@type": "Object", "fields": [{"name": "earliestArrival", "schema": "dateTime"}, {"name": "latestArrival", "schema": "dateTime"}]}}, {"name": "predictionConfidence", "schema": "double"}, {"name": "factorsConsidered", "schema": {"@type": "Array", "elementSchema": "string"}}]}}}, {"@type": "Command", "name": "requestReroute", "displayName": "Request Reroute", "description": "Request dynamic rerouting due to unexpected conditions", "request": {"name": "rerouteRequest", "schema": {"@type": "Object", "fields": [{"name": "reason", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Traffic", "enumValue": "traffic"}, {"name": "Accident", "enumValue": "accident"}, {"name": "Weather", "enumValue": "weather"}, {"name": "VehicleIssue", "enumValue": "vehicle_issue"}, {"name": "CustomerRequest", "enumValue": "customer_request"}, {"name": "Emergency", "enumValue": "emergency"}]}}, {"name": "currentLocation", "schema": {"@type": "Object", "fields": [{"name": "latitude", "schema": "double"}, {"name": "longitude", "schema": "double"}]}}, {"name": "urgency", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Low", "enumValue": "low"}, {"name": "Medium", "enumValue": "medium"}, {"name": "High", "enumValue": "high"}, {"name": "Emergency", "enumValue": "emergency"}]}}]}}}, {"@type": "Relationship", "name": "assignedVehicle", "displayName": "Assigned Vehicle", "target": "dtmi:fleetmanagement:Vehicle;1"}, {"@type": "Relationship", "name": "assignedDriver", "displayName": "Assigned Driver", "target": "dtmi:fleetmanagement:Driver;1"}, {"@type": "Relationship", "name": "routeOptimizer", "displayName": "Route Optimizer", "target": "dtmi:fleetmanagement:RouteOptimizer;1"}]}