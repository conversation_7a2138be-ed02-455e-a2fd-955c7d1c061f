{"@context": "dtmi:dtdl:context;3", "@id": "dtmi:fleetmanagement:Vehicle;1", "@type": "Interface", "displayName": "Fleet Vehicle", "description": "Base digital twin model for fleet vehicles with comprehensive telemetry and AI capabilities", "contents": [{"@type": "Property", "name": "vehicleId", "displayName": "Vehicle ID", "description": "Unique identifier for the vehicle", "schema": "string"}, {"@type": "Property", "name": "vin", "displayName": "VIN", "description": "Vehicle Identification Number", "schema": "string"}, {"@type": "Property", "name": "make", "displayName": "Make", "description": "Vehicle manufacturer", "schema": "string"}, {"@type": "Property", "name": "model", "displayName": "Model", "description": "Vehicle model", "schema": "string"}, {"@type": "Property", "name": "year", "displayName": "Year", "description": "Manufacturing year", "schema": "integer"}, {"@type": "Property", "name": "vehicleType", "displayName": "Vehicle Type", "description": "Type of vehicle", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Car", "enumValue": "car"}, {"name": "Truck", "enumValue": "truck"}, {"name": "<PERSON>", "enumValue": "van"}, {"name": "Bus", "enumValue": "bus"}, {"name": "Motorcycle", "enumValue": "motorcycle"}]}}, {"@type": "Property", "name": "powertrainType", "displayName": "Powertrain Type", "description": "Type of vehicle powertrain", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "ICE", "displayName": "Internal Combustion Engine", "enumValue": "ice"}, {"name": "Electric", "displayName": "Electric", "enumValue": "electric"}, {"name": "Hybrid", "displayName": "Hybrid", "enumValue": "hybrid"}, {"name": "PluginHybrid", "displayName": "Plugin Hybrid", "enumValue": "plugin_hybrid"}]}}, {"@type": "Property", "name": "status", "displayName": "Vehicle Status", "description": "Current operational status", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Active", "enumValue": "active"}, {"name": "Inactive", "enumValue": "inactive"}, {"name": "Maintenance", "enumValue": "maintenance"}, {"name": "OutOfService", "enumValue": "out_of_service"}]}}, {"@type": "Telemetry", "name": "location", "displayName": "GPS Location", "description": "Real-time GPS coordinates", "schema": {"@type": "Object", "fields": [{"name": "latitude", "displayName": "Latitude", "schema": "double"}, {"name": "longitude", "displayName": "Longitude", "schema": "double"}, {"name": "altitude", "displayName": "Altitude", "schema": "double"}, {"name": "heading", "displayName": "Heading (degrees)", "schema": "double"}, {"name": "speed", "displayName": "Speed (km/h)", "schema": "double"}, {"name": "timestamp", "displayName": "Timestamp", "schema": "dateTime"}]}}, {"@type": "Telemetry", "name": "engineDiagnostics", "displayName": "Engine Diagnostics", "description": "Real-time engine performance data", "schema": {"@type": "Object", "fields": [{"name": "engineTemperature", "displayName": "Engine Temperature (°C)", "schema": "double"}, {"name": "oilPressure", "displayName": "Oil Pressure (bar)", "schema": "double"}, {"name": "rpm", "displayName": "Engine RPM", "schema": "integer"}, {"name": "fuelLevel", "displayName": "Fuel Level (%)", "schema": "double"}, {"name": "fuelConsumptionRate", "displayName": "Fuel Consumption Rate (L/100km)", "schema": "double"}, {"name": "engineLoad", "displayName": "Engine Load (%)", "schema": "double"}, {"name": "diagnosticCodes", "displayName": "Diagnostic Trouble Codes", "schema": {"@type": "Array", "elementSchema": "string"}}]}}, {"@type": "Telemetry", "name": "vehicleHealth", "displayName": "Vehicle Health Metrics", "description": "Overall vehicle health and maintenance indicators", "schema": {"@type": "Object", "fields": [{"name": "odometer", "displayName": "Odometer (km)", "schema": "double"}, {"name": "tirePressure", "displayName": "Tire Pressure", "schema": {"@type": "Object", "fields": [{"name": "frontLeft", "schema": "double"}, {"name": "frontRight", "schema": "double"}, {"name": "rearLeft", "schema": "double"}, {"name": "rearRight", "schema": "double"}]}}, {"name": "brakeWearLevel", "displayName": "Brake Wear Level (%)", "schema": "double"}, {"name": "batteryVoltage", "displayName": "12V Battery Voltage", "schema": "double"}, {"name": "maintenanceScore", "displayName": "AI Maintenance Score (0-100)", "schema": "double"}, {"name": "anomalyScore", "displayName": "AI Anomaly Score (0-100)", "schema": "double"}]}}, {"@type": "Command", "name": "requestMaintenance", "displayName": "Request Maintenance", "description": "Schedule maintenance for the vehicle", "request": {"name": "maintenanceRequest", "schema": {"@type": "Object", "fields": [{"name": "maintenanceType", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Routine", "enumValue": "routine"}, {"name": "Preventive", "enumValue": "preventive"}, {"name": "Emergency", "enumValue": "emergency"}, {"name": "Predictive", "enumValue": "predictive"}]}}, {"name": "priority", "schema": {"@type": "Enum", "valueSchema": "string", "enumValues": [{"name": "Low", "enumValue": "low"}, {"name": "Medium", "enumValue": "medium"}, {"name": "High", "enumValue": "high"}, {"name": "Critical", "enumValue": "critical"}]}}, {"name": "description", "schema": "string"}]}}}, {"@type": "Command", "name": "runDiagnostics", "displayName": "Run AI Diagnostics", "description": "Trigger comprehensive AI-powered vehicle diagnostics", "response": {"name": "diagnosticsResult", "schema": {"@type": "Object", "fields": [{"name": "overallHealth", "schema": "double"}, {"name": "predictedIssues", "schema": {"@type": "Array", "elementSchema": "string"}}, {"name": "recommendations", "schema": "string"}]}}}, {"@type": "Relationship", "name": "assignedDriver", "displayName": "Assigned Driver", "target": "dtmi:fleetmanagement:Driver;1"}, {"@type": "Relationship", "name": "currentRoute", "displayName": "Current Route", "target": "dtmi:fleetmanagement:Route;1"}, {"@type": "Relationship", "name": "maintenanceSchedule", "displayName": "Maintenance Schedule", "target": "dtmi:fleetmanagement:MaintenanceSchedule;1"}, {"@type": "Relationship", "name": "predictiveModel", "displayName": "Predictive Maintenance Model", "target": "dtmi:fleetmanagement:PredictiveMaintenanceModel;1"}, {"@type": "Relationship", "name": "anomalyDetector", "displayName": "Anomaly Detection Model", "target": "dtmi:fleetmanagement:AnomalyDetectionModel;1"}]}