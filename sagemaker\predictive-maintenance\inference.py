#!/usr/bin/env python3

import json
import joblib
import numpy as np
import pandas as pd
import os
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global variables for model caching
model_artifact = None

def model_fn(model_dir):
    """
    Load the model for inference
    """
    global model_artifact
    
    try:
        model_path = os.path.join(model_dir, 'model.joblib')
        model_artifact = joblib.load(model_path)
        
        logger.info("Predictive maintenance model loaded successfully")
        logger.info(f"Model version: {model_artifact.get('model_version', 'unknown')}")
        logger.info(f"Features: {len(model_artifact.get('feature_names', []))}")
        
        return model_artifact
        
    except Exception as e:
        logger.error(f"Error loading model: {str(e)}")
        raise

def input_fn(request_body, request_content_type):
    """
    Parse input data for inference
    """
    try:
        if request_content_type == 'application/json':
            input_data = json.loads(request_body)
            
            # Handle different input formats
            if 'instances' in input_data:
                # Batch prediction format
                instances = input_data['instances']
                if isinstance(instances[0], list):
                    # Raw feature vectors
                    return np.array(instances)
                else:
                    # Feature dictionaries
                    return pd.DataFrame(instances)
            elif 'features' in input_data:
                # Single prediction with feature dictionary
                return pd.DataFrame([input_data['features']])
            elif isinstance(input_data, list):
                # Direct feature vector
                return np.array([input_data])
            else:
                # Single feature dictionary
                return pd.DataFrame([input_data])
        
        elif request_content_type == 'text/csv':
            # CSV format
            from io import StringIO
            df = pd.read_csv(StringIO(request_body))
            return df
        
        else:
            raise ValueError(f"Unsupported content type: {request_content_type}")
            
    except Exception as e:
        logger.error(f"Error parsing input: {str(e)}")
        raise

def predict_fn(input_data, model):
    """
    Make predictions using the loaded model
    """
    try:
        classifier = model['classifier']
        regressor = model['regressor']
        scaler = model['scaler']
        feature_names = model['feature_names']
        
        # Prepare features
        if isinstance(input_data, pd.DataFrame):
            features = prepare_features_from_dataframe(input_data, feature_names)
        else:
            features = input_data
        
        # Ensure we have the right number of features
        if features.shape[1] != len(feature_names):
            logger.warning(f"Feature count mismatch. Expected {len(feature_names)}, got {features.shape[1]}")
            # Pad or truncate as needed
            if features.shape[1] < len(feature_names):
                padding = np.zeros((features.shape[0], len(feature_names) - features.shape[1]))
                features = np.hstack([features, padding])
            else:
                features = features[:, :len(feature_names)]
        
        # Scale features
        features_scaled = scaler.transform(features)
        
        # Make predictions
        failure_probabilities = classifier.predict_proba(features_scaled)[:, 1]
        days_to_failure = regressor.predict(features_scaled)
        
        # Calculate confidence scores
        confidence_scores = calculate_confidence(classifier, regressor, features_scaled)
        
        # Prepare results
        predictions = []
        for i in range(len(features)):
            prediction = {
                'failure_probability': float(failure_probabilities[i]),
                'days_to_failure': max(1, int(days_to_failure[i])),  # Ensure at least 1 day
                'confidence': float(confidence_scores[i]),
                'risk_level': determine_risk_level(failure_probabilities[i]),
                'recommendation': generate_recommendation(failure_probabilities[i], days_to_failure[i]),
                'prediction_timestamp': datetime.now().isoformat()
            }
            predictions.append(prediction)
        
        return predictions
        
    except Exception as e:
        logger.error(f"Error making predictions: {str(e)}")
        raise

def prepare_features_from_dataframe(df, feature_names):
    """
    Prepare feature matrix from DataFrame
    """
    try:
        # Create feature matrix with proper ordering
        features = np.zeros((len(df), len(feature_names)))
        
        for i, feature_name in enumerate(feature_names):
            if feature_name in df.columns:
                features[:, i] = df[feature_name].fillna(0).values
            else:
                # Try to calculate derived features
                if feature_name == 'engine_temp_mean' and 'engineTemperature' in df.columns:
                    features[:, i] = df['engineTemperature'].fillna(0).values
                elif feature_name == 'oil_pressure_mean' and 'oilPressure' in df.columns:
                    features[:, i] = df['oilPressure'].fillna(0).values
                elif feature_name == 'rpm_mean' and 'rpm' in df.columns:
                    features[:, i] = df['rpm'].fillna(0).values
                elif feature_name == 'fuel_consumption_mean' and 'fuelConsumptionRate' in df.columns:
                    features[:, i] = df['fuelConsumptionRate'].fillna(0).values
                elif feature_name == 'brake_wear' and 'brakeWearLevel' in df.columns:
                    features[:, i] = df['brakeWearLevel'].fillna(0).values
                elif feature_name == 'battery_voltage_mean' and 'batteryVoltage' in df.columns:
                    features[:, i] = df['batteryVoltage'].fillna(0).values
                elif feature_name == 'tire_pressure_variance':
                    # Calculate tire pressure variance if individual tire pressures are available
                    tire_cols = [col for col in df.columns if 'tirePressure' in col]
                    if tire_cols:
                        features[:, i] = df[tire_cols].var(axis=1).fillna(0).values
                else:
                    # Default to 0 for missing features
                    features[:, i] = 0.0
        
        return features
        
    except Exception as e:
        logger.error(f"Error preparing features: {str(e)}")
        raise

def calculate_confidence(classifier, regressor, features):
    """
    Calculate confidence scores for predictions
    """
    try:
        # Get prediction probabilities for classification
        class_probabilities = classifier.predict_proba(features)
        
        # Calculate confidence based on the maximum probability
        class_confidence = np.max(class_probabilities, axis=1)
        
        # For regression, use the variance of predictions from individual trees
        # This is a proxy for uncertainty
        tree_predictions = np.array([tree.predict(features) for tree in regressor.estimators_])
        reg_variance = np.var(tree_predictions, axis=0)
        
        # Normalize regression confidence (lower variance = higher confidence)
        reg_confidence = 1.0 / (1.0 + reg_variance / 100)  # Scale factor of 100
        
        # Combine confidences (weighted average)
        combined_confidence = 0.6 * class_confidence + 0.4 * reg_confidence
        
        return combined_confidence
        
    except Exception as e:
        logger.error(f"Error calculating confidence: {str(e)}")
        return np.ones(len(features)) * 0.5  # Default confidence

def determine_risk_level(failure_probability):
    """
    Determine risk level based on failure probability
    """
    if failure_probability >= 0.8:
        return 'critical'
    elif failure_probability >= 0.6:
        return 'high'
    elif failure_probability >= 0.4:
        return 'medium'
    elif failure_probability >= 0.2:
        return 'low'
    else:
        return 'minimal'

def generate_recommendation(failure_probability, days_to_failure):
    """
    Generate maintenance recommendation based on predictions
    """
    if failure_probability >= 0.8:
        return 'immediate_maintenance_required'
    elif failure_probability >= 0.6:
        return 'schedule_maintenance_within_week'
    elif failure_probability >= 0.4:
        return 'schedule_maintenance_within_month'
    elif days_to_failure <= 30:
        return 'monitor_closely'
    elif days_to_failure <= 90:
        return 'routine_monitoring'
    else:
        return 'normal_operation'

def output_fn(prediction, accept):
    """
    Format the prediction output
    """
    try:
        if accept == 'application/json':
            return json.dumps({
                'predictions': prediction,
                'model_version': model_artifact.get('model_version', '1.0'),
                'prediction_timestamp': datetime.now().isoformat()
            })
        elif accept == 'text/csv':
            # Convert to CSV format
            df = pd.DataFrame(prediction)
            return df.to_csv(index=False)
        else:
            raise ValueError(f"Unsupported accept type: {accept}")
            
    except Exception as e:
        logger.error(f"Error formatting output: {str(e)}")
        raise

# Health check endpoint
def ping():
    """
    Health check for the model endpoint
    """
    try:
        if model_artifact is not None:
            return {
                'status': 'healthy',
                'model_version': model_artifact.get('model_version', 'unknown'),
                'timestamp': datetime.now().isoformat()
            }
        else:
            return {
                'status': 'unhealthy',
                'error': 'Model not loaded',
                'timestamp': datetime.now().isoformat()
            }
    except Exception as e:
        return {
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }

# Batch transform support
def transform_fn(model, request_body, request_content_type, accept):
    """
    Transform function for batch inference
    """
    try:
        # Parse input
        input_data = input_fn(request_body, request_content_type)
        
        # Make predictions
        predictions = predict_fn(input_data, model)
        
        # Format output
        return output_fn(predictions, accept)
        
    except Exception as e:
        logger.error(f"Error in transform function: {str(e)}")
        error_response = {
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }
        return json.dumps(error_response)
