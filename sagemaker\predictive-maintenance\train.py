#!/usr/bin/env python3

import argparse
import os
import pandas as pd
import numpy as np
import joblib
import json
import logging
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import classification_report, mean_squared_error, r2_score
import boto3

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def parse_args():
    """
    Parse command line arguments
    """
    parser = argparse.ArgumentParser()
    
    # SageMaker specific arguments
    parser.add_argument('--model-dir', type=str, default=os.environ.get('SM_MODEL_DIR'))
    parser.add_argument('--train', type=str, default=os.environ.get('SM_CHANNEL_TRAIN'))
    parser.add_argument('--validation', type=str, default=os.environ.get('SM_CHANNEL_VALIDATION'))
    
    # Model hyperparameters
    parser.add_argument('--n-estimators', type=int, default=100)
    parser.add_argument('--max-depth', type=int, default=10)
    parser.add_argument('--min-samples-split', type=int, default=5)
    parser.add_argument('--min-samples-leaf', type=int, default=2)
    parser.add_argument('--random-state', type=int, default=42)
    
    # Training parameters
    parser.add_argument('--test-size', type=float, default=0.2)
    parser.add_argument('--validation-size', type=float, default=0.2)
    parser.add_argument('--perform-grid-search', type=bool, default=False)
    
    return parser.parse_args()

def load_data(train_path, validation_path=None):
    """
    Load training and validation data
    """
    logger.info(f"Loading training data from {train_path}")
    
    # Load training data
    train_files = [f for f in os.listdir(train_path) if f.endswith('.csv')]
    train_data = []
    
    for file in train_files:
        df = pd.read_csv(os.path.join(train_path, file))
        train_data.append(df)
    
    train_df = pd.concat(train_data, ignore_index=True)
    logger.info(f"Loaded {len(train_df)} training samples")
    
    # Load validation data if provided
    validation_df = None
    if validation_path and os.path.exists(validation_path):
        logger.info(f"Loading validation data from {validation_path}")
        val_files = [f for f in os.listdir(validation_path) if f.endswith('.csv')]
        val_data = []
        
        for file in val_files:
            df = pd.read_csv(os.path.join(validation_path, file))
            val_data.append(df)
        
        validation_df = pd.concat(val_data, ignore_index=True)
        logger.info(f"Loaded {len(validation_df)} validation samples")
    
    return train_df, validation_df

def preprocess_data(df):
    """
    Preprocess the data for training
    """
    logger.info("Preprocessing data...")
    
    # Define feature columns
    feature_columns = [
        'engine_temp_mean', 'engine_temp_std', 'engine_temp_max',
        'oil_pressure_mean', 'oil_pressure_std', 'oil_pressure_min',
        'rpm_mean', 'rpm_std', 'rpm_max',
        'fuel_consumption_mean', 'fuel_consumption_trend',
        'brake_wear', 'battery_voltage_mean', 'battery_voltage_std',
        'tire_pressure_variance', 'odometer', 'vehicle_age_days',
        'maintenance_history_count', 'last_maintenance_days'
    ]
    
    # Create additional features if they don't exist
    if 'vehicle_age_days' not in df.columns:
        df['vehicle_age_days'] = (pd.to_datetime('today') - pd.to_datetime(df.get('manufacture_date', '2020-01-01'))).dt.days
    
    if 'maintenance_history_count' not in df.columns:
        df['maintenance_history_count'] = df.get('maintenance_count', 0)
    
    if 'last_maintenance_days' not in df.columns:
        df['last_maintenance_days'] = (pd.to_datetime('today') - pd.to_datetime(df.get('last_maintenance_date', '2020-01-01'))).dt.days
    
    # Select available features
    available_features = [col for col in feature_columns if col in df.columns]
    logger.info(f"Using {len(available_features)} features: {available_features}")
    
    # Handle missing values
    df[available_features] = df[available_features].fillna(df[available_features].median())
    
    # Prepare features and targets
    X = df[available_features].values
    
    # Prepare targets for both classification and regression
    y_classification = df.get('failure_within_30_days', 0).values  # Binary classification
    y_regression = df.get('days_to_failure', 365).values  # Regression target
    
    # Handle infinite values
    X = np.nan_to_num(X, nan=0.0, posinf=1e6, neginf=-1e6)
    y_regression = np.nan_to_num(y_regression, nan=365, posinf=365, neginf=0)
    
    return X, y_classification, y_regression, available_features

def train_models(X_train, y_class_train, y_reg_train, X_val, y_class_val, y_reg_val, args):
    """
    Train both classification and regression models
    """
    logger.info("Training predictive maintenance models...")
    
    # Scale features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_val_scaled = scaler.transform(X_val) if X_val is not None else None
    
    models = {}
    
    # Train classification model (failure prediction)
    logger.info("Training failure classification model...")
    
    if args.perform_grid_search:
        # Grid search for best parameters
        param_grid = {
            'n_estimators': [50, 100, 200],
            'max_depth': [5, 10, 15],
            'min_samples_split': [2, 5, 10],
            'min_samples_leaf': [1, 2, 4]
        }
        
        rf_classifier = RandomForestClassifier(random_state=args.random_state)
        grid_search = GridSearchCV(rf_classifier, param_grid, cv=3, scoring='f1', n_jobs=-1)
        grid_search.fit(X_train_scaled, y_class_train)
        
        classifier = grid_search.best_estimator_
        logger.info(f"Best classification parameters: {grid_search.best_params_}")
    else:
        classifier = RandomForestClassifier(
            n_estimators=args.n_estimators,
            max_depth=args.max_depth,
            min_samples_split=args.min_samples_split,
            min_samples_leaf=args.min_samples_leaf,
            random_state=args.random_state,
            class_weight='balanced'
        )
        classifier.fit(X_train_scaled, y_class_train)
    
    models['classifier'] = classifier
    
    # Train regression model (days to failure)
    logger.info("Training days-to-failure regression model...")
    
    if args.perform_grid_search:
        param_grid = {
            'n_estimators': [50, 100, 200],
            'max_depth': [5, 10, 15],
            'min_samples_split': [2, 5, 10],
            'min_samples_leaf': [1, 2, 4]
        }
        
        rf_regressor = RandomForestRegressor(random_state=args.random_state)
        grid_search = GridSearchCV(rf_regressor, param_grid, cv=3, scoring='neg_mean_squared_error', n_jobs=-1)
        grid_search.fit(X_train_scaled, y_reg_train)
        
        regressor = grid_search.best_estimator_
        logger.info(f"Best regression parameters: {grid_search.best_params_}")
    else:
        regressor = RandomForestRegressor(
            n_estimators=args.n_estimators,
            max_depth=args.max_depth,
            min_samples_split=args.min_samples_split,
            min_samples_leaf=args.min_samples_leaf,
            random_state=args.random_state
        )
        regressor.fit(X_train_scaled, y_reg_train)
    
    models['regressor'] = regressor
    models['scaler'] = scaler
    
    # Evaluate models
    if X_val is not None:
        evaluate_models(models, X_val_scaled, y_class_val, y_reg_val)
    
    return models

def evaluate_models(models, X_val, y_class_val, y_reg_val):
    """
    Evaluate the trained models
    """
    logger.info("Evaluating models...")
    
    classifier = models['classifier']
    regressor = models['regressor']
    
    # Classification evaluation
    y_class_pred = classifier.predict(X_val)
    y_class_proba = classifier.predict_proba(X_val)[:, 1]
    
    logger.info("Classification Report:")
    logger.info(classification_report(y_class_val, y_class_pred))
    
    # Regression evaluation
    y_reg_pred = regressor.predict(X_val)
    mse = mean_squared_error(y_reg_val, y_reg_pred)
    r2 = r2_score(y_reg_val, y_reg_pred)
    
    logger.info(f"Regression MSE: {mse:.2f}")
    logger.info(f"Regression R²: {r2:.3f}")
    
    # Feature importance
    feature_importance_class = classifier.feature_importances_
    feature_importance_reg = regressor.feature_importances_
    
    logger.info("Top 5 features for classification:")
    for i in np.argsort(feature_importance_class)[-5:]:
        logger.info(f"  Feature {i}: {feature_importance_class[i]:.3f}")
    
    logger.info("Top 5 features for regression:")
    for i in np.argsort(feature_importance_reg)[-5:]:
        logger.info(f"  Feature {i}: {feature_importance_reg[i]:.3f}")

def save_model(models, feature_names, model_dir):
    """
    Save the trained models
    """
    logger.info(f"Saving models to {model_dir}")
    
    # Create model artifact
    model_artifact = {
        'classifier': models['classifier'],
        'regressor': models['regressor'],
        'scaler': models['scaler'],
        'feature_names': feature_names,
        'model_version': '1.0',
        'training_timestamp': pd.Timestamp.now().isoformat()
    }
    
    # Save using joblib
    model_path = os.path.join(model_dir, 'model.joblib')
    joblib.dump(model_artifact, model_path)
    
    # Save model metadata
    metadata = {
        'model_type': 'predictive_maintenance',
        'algorithm': 'random_forest',
        'features': feature_names,
        'target_classification': 'failure_within_30_days',
        'target_regression': 'days_to_failure',
        'model_version': '1.0',
        'training_timestamp': pd.Timestamp.now().isoformat()
    }
    
    metadata_path = os.path.join(model_dir, 'metadata.json')
    with open(metadata_path, 'w') as f:
        json.dump(metadata, f, indent=2)
    
    logger.info("Models saved successfully")

def main():
    """
    Main training function
    """
    args = parse_args()
    
    logger.info("Starting predictive maintenance model training...")
    logger.info(f"Arguments: {vars(args)}")
    
    # Load data
    train_df, validation_df = load_data(args.train, args.validation)
    
    # Preprocess data
    X, y_classification, y_regression, feature_names = preprocess_data(train_df)
    
    # Split data if no separate validation set
    if validation_df is None:
        X_train, X_val, y_class_train, y_class_val, y_reg_train, y_reg_val = train_test_split(
            X, y_classification, y_regression,
            test_size=args.test_size,
            random_state=args.random_state,
            stratify=y_classification
        )
    else:
        X_train, y_class_train, y_reg_train = X, y_classification, y_regression
        X_val, y_class_val, y_reg_val, _ = preprocess_data(validation_df)
    
    logger.info(f"Training set size: {X_train.shape}")
    logger.info(f"Validation set size: {X_val.shape if X_val is not None else 'None'}")
    
    # Train models
    models = train_models(X_train, y_class_train, y_reg_train, X_val, y_class_val, y_reg_val, args)
    
    # Save models
    save_model(models, feature_names, args.model_dir)
    
    logger.info("Training completed successfully!")

if __name__ == '__main__':
    main()
