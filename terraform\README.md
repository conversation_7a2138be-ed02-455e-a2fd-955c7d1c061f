# Fleet Management Digital Twins - Terraform Deployment

This directory contains Terraform configuration files for deploying the complete Fleet Management Digital Twins solution on AWS.

## 🏗️ Architecture Overview

The Terraform configuration deploys the following AWS resources:

### Core Infrastructure
- **S3 Buckets**: TwinMaker workspace, data lake, Lambda deployments
- **Amazon Timestream**: Time-series database with 3 tables
- **Amazon Kinesis**: 4 data streams for real-time processing
- **AWS Lambda**: 3 functions for data processing and AI inference
- **Amazon SNS**: 2 topics for alerts and notifications

### IoT and Digital Twins
- **AWS IoT Core**: Thing types, policies, and topic rules
- **AWS IoT TwinMaker**: Component types and workspace (created post-deployment)
- **IoT Device Defender**: Security profiles and monitoring

### AI/ML Services
- **Amazon SageMaker**: Domain, endpoints, and model registry
- **SageMaker Pipelines**: Automated model training workflows
- **Model Monitoring**: Data quality and model drift detection

### Data Analytics
- **AWS Glue**: Data catalog and crawlers
- **Amazon Athena**: Query workgroup and named queries
- **Kinesis Analytics**: Real-time stream processing
- **<PERSON>nes<PERSON>**: Data lake ingestion

### Monitoring and Security
- **CloudWatch**: Dashboards, log groups, and metrics
- **I<PERSON>**: Roles and policies with least-privilege access
- **EventBridge**: Scheduled tasks and automation

## 📋 Prerequisites

- AWS CLI configured with appropriate permissions
- Terraform >= 1.0 installed
- Python 3.8+ installed
- jq installed (for JSON processing)

## 🚀 Quick Start

### 1. Automated Deployment
```bash
# Make the deployment script executable
chmod +x ../deploy-terraform.sh

# Run the automated deployment
../deploy-terraform.sh
```

### 2. Manual Deployment

#### Step 1: Configure Variables
```bash
# Copy the example variables file
cp terraform.tfvars.example terraform.tfvars

# Edit the variables file with your preferences
vim terraform.tfvars
```

#### Step 2: Initialize Terraform
```bash
# Initialize Terraform
terraform init

# Create or select workspace
terraform workspace new dev  # or staging/prod
```

#### Step 3: Plan and Apply
```bash
# Plan the deployment
terraform plan -var="environment=dev" -out=tfplan

# Apply the deployment
terraform apply tfplan
```

#### Step 4: Post-Deployment Setup
```bash
# Create TwinMaker workspace
aws iottwinmaker create-workspace \
    --workspace-id fleet-management-workspace \
    --description "Fleet Management Digital Twins" \
    --s3-location s3://$(terraform output -raw twinmaker_bucket_name) \
    --role $(terraform output -raw twinmaker_execution_role_arn)

# Create component types
aws iottwinmaker create-component-type \
    --workspace-id fleet-management-workspace \
    --component-type-id VehicleComponent \
    --property-definitions file://../aws-config/vehicle-properties.json
```

## 📁 File Structure

```
terraform/
├── main.tf                    # Main Terraform configuration
├── variables.tf               # Variable definitions
├── outputs.tf                 # Output definitions
├── iam.tf                     # IAM roles and policies
├── lambda.tf                  # Lambda functions
├── iot.tf                     # IoT Core resources
├── kinesis.tf                 # Kinesis streams and analytics
├── data-catalog.tf            # Glue catalog and Athena
├── sagemaker.tf               # SageMaker resources
├── terraform.tfvars.example  # Example variables
└── README.md                  # This file
```

## ⚙️ Configuration Variables

### Required Variables
- `aws_region`: AWS region for deployment
- `environment`: Environment name (dev/staging/prod)
- `project_name`: Project name for resource naming

### Optional Variables
- `kinesis_shard_count`: Number of Kinesis shards (default: 5)
- `lambda_memory_size`: Lambda memory allocation (default: 512MB)
- `enable_sagemaker`: Enable SageMaker resources (default: true)
- `enable_vpc`: Deploy in VPC (default: false)
- `notification_email`: Email for SNS notifications

See `terraform.tfvars.example` for all available variables.

## 🔧 Customization

### Adding New Lambda Functions
1. Create the function directory in `../aws-lambda/`
2. Add the function configuration to `lambda.tf`
3. Update IAM permissions in `iam.tf`

### Modifying Kinesis Streams
1. Update stream configuration in `main.tf`
2. Adjust shard count in `variables.tf`
3. Update Firehose configuration in `kinesis.tf`

### Adding SageMaker Models
1. Update model configuration in `sagemaker.tf`
2. Add model-specific IAM permissions
3. Update pipeline configuration

## 📊 Monitoring and Observability

### CloudWatch Dashboard
Access the automatically created dashboard:
```bash
aws cloudwatch get-dashboard \
    --dashboard-name $(terraform output -raw cloudwatch_dashboard_name)
```

### Log Groups
Monitor Lambda function logs:
```bash
aws logs describe-log-groups \
    --log-group-name-prefix /aws/lambda/fleet
```

### Kinesis Metrics
Monitor stream performance:
```bash
aws kinesis describe-stream \
    --stream-name $(terraform output -raw vehicle_telemetry_stream_name)
```

## 🔒 Security

### IAM Roles
The deployment creates least-privilege IAM roles:
- `TwinMakerExecutionRole`: For TwinMaker operations
- `FleetLambdaExecutionRole`: For Lambda functions
- `SageMakerExecutionRole`: For SageMaker operations
- `IoTRuleExecutionRole`: For IoT rule actions

### Encryption
- S3 buckets: Server-side encryption (AES-256)
- Kinesis streams: KMS encryption
- Timestream: Encryption at rest
- Lambda: Environment variable encryption

### Network Security
- VPC deployment option available
- Private subnets for sensitive resources
- Security groups with minimal access

## 💰 Cost Optimization

### Resource Sizing
- Lambda: Right-sized memory allocation
- Kinesis: Auto-scaling enabled
- Timestream: Optimized retention policies
- S3: Lifecycle rules for cost optimization

### Monitoring Costs
```bash
# View cost allocation tags
aws ce get-cost-and-usage \
    --time-period Start=2024-01-01,End=2024-01-31 \
    --granularity MONTHLY \
    --metrics BlendedCost \
    --group-by Type=DIMENSION,Key=SERVICE
```

## 🧪 Testing

### Validate Deployment
```bash
# Check Terraform state
terraform show

# Validate resources
terraform plan -detailed-exitcode
```

### Test Data Flow
```bash
# Run vehicle simulator
cd ../aws-testing
python3 vehicle_simulator.py --vehicles 5 --duration 10
```

### Query Data
```bash
# Query Timestream data
aws timestream-query query \
    --query-string "SELECT * FROM FleetManagementDB.VehicleTelemetry LIMIT 10"
```

## 🔄 Updates and Maintenance

### Updating Infrastructure
```bash
# Pull latest changes
git pull

# Plan updates
terraform plan

# Apply updates
terraform apply
```

### Updating Lambda Functions
```bash
# Update function code
terraform apply -target=aws_lambda_function.telemetry_processor
```

### Scaling Resources
```bash
# Update variables
vim terraform.tfvars

# Apply changes
terraform apply
```

## 🗑️ Cleanup

### Destroy Resources
```bash
# Destroy all resources
terraform destroy

# Destroy specific resources
terraform destroy -target=aws_sagemaker_endpoint.predictive_maintenance
```

### Manual Cleanup
Some resources may need manual cleanup:
- TwinMaker workspace and entities
- S3 bucket contents
- CloudWatch log data

## 🆘 Troubleshooting

### Common Issues

#### Terraform Init Fails
```bash
# Clear cache and reinitialize
rm -rf .terraform
terraform init
```

#### Lambda Deployment Fails
```bash
# Check package size
ls -la lambda-packages/

# Recreate packages
rm -rf lambda-packages/
terraform apply
```

#### Permission Errors
```bash
# Check AWS credentials
aws sts get-caller-identity

# Verify IAM permissions
aws iam simulate-principal-policy \
    --policy-source-arn $(aws sts get-caller-identity --query Arn --output text) \
    --action-names iot:CreateThing
```

### Getting Help
- Check Terraform logs: `TF_LOG=DEBUG terraform apply`
- Review AWS CloudTrail for API errors
- Check resource limits in AWS Service Quotas

## 📚 Additional Resources

- [AWS IoT TwinMaker Documentation](https://docs.aws.amazon.com/iot-twinmaker/)
- [Terraform AWS Provider](https://registry.terraform.io/providers/hashicorp/aws/latest/docs)
- [AWS Well-Architected Framework](https://aws.amazon.com/architecture/well-architected/)
- [Fleet Management Best Practices](https://aws.amazon.com/solutions/implementations/connected-vehicle-solution/)
