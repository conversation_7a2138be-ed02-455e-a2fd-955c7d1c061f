# AWS Glue Data Catalog for Fleet Management

# Glue Catalog Database
resource "aws_glue_catalog_database" "fleet_data_catalog" {
  name        = "fleet_management_catalog"
  description = "Data catalog for fleet management telemetry data"

  tags = merge(local.common_tags, {
    Name = "Fleet Data Catalog"
  })
}

# Glue Catalog Table for Vehicle Telemetry
resource "aws_glue_catalog_table" "vehicle_telemetry_table" {
  name          = "vehicle_telemetry"
  database_name = aws_glue_catalog_database.fleet_data_catalog.name
  description   = "Vehicle telemetry data table"

  table_type = "EXTERNAL_TABLE"

  parameters = {
    "classification"                   = "parquet"
    "compressionType"                 = "gzip"
    "typeOfData"                      = "file"
    "has_encrypted_data"              = "false"
    "projection.enabled"              = "true"
    "projection.year.type"            = "integer"
    "projection.year.range"           = "2020,2030"
    "projection.month.type"           = "integer"
    "projection.month.range"          = "1,12"
    "projection.month.digits"         = "2"
    "projection.day.type"             = "integer"
    "projection.day.range"            = "1,31"
    "projection.day.digits"           = "2"
    "projection.hour.type"            = "integer"
    "projection.hour.range"           = "0,23"
    "projection.hour.digits"          = "2"
    "storage.location.template"       = "s3://${aws_s3_bucket.data_lake.bucket}/telemetry/vehicle/year=$${year}/month=$${month}/day=$${day}/hour=$${hour}/"
  }

  storage_descriptor {
    location      = "s3://${aws_s3_bucket.data_lake.bucket}/telemetry/vehicle/"
    input_format  = "org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat"
    output_format = "org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat"

    ser_de_info {
      serialization_library = "org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe"
    }

    columns {
      name = "vehicleid"
      type = "string"
    }

    columns {
      name = "timestamp"
      type = "timestamp"
    }

    columns {
      name = "location"
      type = "struct<latitude:double,longitude:double,altitude:double,heading:double,speed:double>"
    }

    columns {
      name = "enginediagnostics"
      type = "struct<enginetemperature:double,oilpressure:double,rpm:int,fuellevel:double,fuelconsumptionrate:double,engineload:double>"
    }

    columns {
      name = "vehiclehealth"
      type = "struct<odometer:double,tirepressurefrontleft:double,tirepressurefrontright:double,tirepressurerearleft:double,tirepressurerearright:double,brakewearlevel:double,batteryvoltage:double,maintenancescore:double,anomalyscore:double>"
    }

    columns {
      name = "batterystatus"
      type = "struct<stateofcharge:double,stateofhealth:double,batterytemperature:double,voltage:double,current:double,power:double,estimatedrange:double,energyconsumptionrate:double,regenerativebrakingeneregy:double>"
    }

    columns {
      name = "chargingstatus"
      type = "struct<ischarging:boolean,chargingpower:double,chargingvoltage:double,chargingcurrent:double,timetofullcharge:int,chargingefficiency:double,chargingcost:double,connectedstationid:string>"
    }

    columns {
      name = "year"
      type = "int"
    }

    columns {
      name = "month"
      type = "int"
    }

    columns {
      name = "day"
      type = "int"
    }

    columns {
      name = "hour"
      type = "int"
    }
  }

  partition_keys {
    name = "year"
    type = "int"
  }

  partition_keys {
    name = "month"
    type = "int"
  }

  partition_keys {
    name = "day"
    type = "int"
  }

  partition_keys {
    name = "hour"
    type = "int"
  }
}

# Glue Catalog Table for Driver Behavior
resource "aws_glue_catalog_table" "driver_behavior_table" {
  name          = "driver_behavior"
  database_name = aws_glue_catalog_database.fleet_data_catalog.name
  description   = "Driver behavior data table"

  table_type = "EXTERNAL_TABLE"

  parameters = {
    "classification"  = "json"
    "compressionType" = "gzip"
    "typeOfData"      = "file"
  }

  storage_descriptor {
    location      = "s3://${aws_s3_bucket.data_lake.bucket}/telemetry/driver/"
    input_format  = "org.apache.hadoop.mapred.TextInputFormat"
    output_format = "org.apache.hadoop.hive.ql.io.HiveIgnoreKeyTextOutputFormat"

    ser_de_info {
      serialization_library = "org.openx.data.jsonserde.JsonSerDe"
    }

    columns {
      name = "driverid"
      type = "string"
    }

    columns {
      name = "timestamp"
      type = "timestamp"
    }

    columns {
      name = "drivingbehavior"
      type = "struct<averagespeed:double,maxspeed:double,harshaccelerationevents:int,harshbrakingevents:int,sharpturnvents:int,speedingevents:int,idlingtime:double,fuelefficiencyscore:double,safetyscore:double,ecoscore:double>"
    }

    columns {
      name = "workinghours"
      type = "struct<shiftstarttime:string,shiftendtime:string,totaldrivingtime:double,breaktime:double,overtimehours:double,compliancestatus:string>"
    }

    columns {
      name = "performancemetrics"
      type = "struct<totalmilesdrive:double,deliveriescompleted:int,ontimedeliveryrate:double,customerrating:double,incidentcount:int,trainingcompletionrate:double,overallperformancescore:double>"
    }
  }
}

# Glue Catalog Table for Charging Data
resource "aws_glue_catalog_table" "charging_data_table" {
  name          = "charging_data"
  database_name = aws_glue_catalog_database.fleet_data_catalog.name
  description   = "Charging station data table"

  table_type = "EXTERNAL_TABLE"

  parameters = {
    "classification"  = "json"
    "compressionType" = "gzip"
    "typeOfData"      = "file"
  }

  storage_descriptor {
    location      = "s3://${aws_s3_bucket.data_lake.bucket}/telemetry/charging/"
    input_format  = "org.apache.hadoop.mapred.TextInputFormat"
    output_format = "org.apache.hadoop.hive.ql.io.HiveIgnoreKeyTextOutputFormat"

    ser_de_info {
      serialization_library = "org.openx.data.jsonserde.JsonSerDe"
    }

    columns {
      name = "stationid"
      type = "string"
    }

    columns {
      name = "timestamp"
      type = "timestamp"
    }

    columns {
      name = "stationstatus"
      type = "struct<operationalstatus:string,availableconnectors:int,totalconnectors:int,currentload:double,maxcapacity:double,energydelivered:double,sessionsactive:int,queuelength:int,averagewaittime:double>"
    }

    columns {
      name = "energymanagement"
      type = "struct<gridconnectionstatus:string,powerdemand:double,energycost:double,renewableenergyusage:double,loadbalancingstatus:string,peakdemandtoday:double,energyefficiency:double>"
    }

    columns {
      name = "maintenancestatus"
      type = "struct<lastmaintenancedate:string,nextmaintenancedue:string,healthscore:double,faultcodes:array<string>,maintenancealerts:array<string>>"
    }
  }
}

# Glue Crawler for automatic schema discovery
resource "aws_glue_crawler" "fleet_data_crawler" {
  database_name = aws_glue_catalog_database.fleet_data_catalog.name
  name          = "fleet-data-crawler"
  role          = aws_iam_role.glue_crawler_role.arn

  s3_target {
    path = "s3://${aws_s3_bucket.data_lake.bucket}/telemetry/"
  }

  configuration = jsonencode({
    "Version" = 1.0
    "CrawlerOutput" = {
      "Partitions" = {
        "AddOrUpdateBehavior" = "InheritFromTable"
      }
    }
    "Grouping" = {
      "TableGroupingPolicy" = "CombineCompatibleSchemas"
    }
  })

  schedule = "cron(0 6 * * ? *)"  # Daily at 6 AM UTC

  tags = merge(local.common_tags, {
    Name = "Fleet Data Crawler"
  })
}

# IAM Role for Glue Crawler
resource "aws_iam_role" "glue_crawler_role" {
  name = "GlueCrawlerRole"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "glue.amazonaws.com"
        }
      }
    ]
  })

  tags = merge(local.common_tags, {
    Name = "Glue Crawler Role"
  })
}

resource "aws_iam_role_policy_attachment" "glue_service_role" {
  role       = aws_iam_role.glue_crawler_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSGlueServiceRole"
}

resource "aws_iam_role_policy" "glue_s3_access" {
  name = "GlueS3Access"
  role = aws_iam_role.glue_crawler_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:ListBucket"
        ]
        Resource = [
          aws_s3_bucket.data_lake.arn,
          "${aws_s3_bucket.data_lake.arn}/*"
        ]
      }
    ]
  })
}

# Athena Workgroup for querying data
resource "aws_athena_workgroup" "fleet_analytics" {
  name = "fleet-analytics"

  configuration {
    enforce_workgroup_configuration    = true
    publish_cloudwatch_metrics_enabled = true

    result_configuration {
      output_location = "s3://${aws_s3_bucket.data_lake.bucket}/athena-results/"

      encryption_configuration {
        encryption_option = "SSE_S3"
      }
    }
  }

  tags = merge(local.common_tags, {
    Name = "Fleet Analytics Workgroup"
  })
}

# Athena Named Queries for common analytics
resource "aws_athena_named_query" "vehicle_health_summary" {
  name      = "vehicle_health_summary"
  workgroup = aws_athena_workgroup.fleet_analytics.id
  database  = aws_glue_catalog_database.fleet_data_catalog.name

  query = <<EOF
SELECT 
    vehicleid,
    DATE(timestamp) as date,
    AVG(vehiclehealth.maintenancescore) as avg_maintenance_score,
    AVG(vehiclehealth.anomalyscore) as avg_anomaly_score,
    MAX(enginediagnostics.enginetemperature) as max_engine_temp,
    MIN(enginediagnostics.oilpressure) as min_oil_pressure,
    AVG(location.speed) as avg_speed,
    COUNT(*) as record_count
FROM vehicle_telemetry
WHERE year = YEAR(CURRENT_DATE)
    AND month = MONTH(CURRENT_DATE)
    AND day = DAY(CURRENT_DATE)
GROUP BY vehicleid, DATE(timestamp)
ORDER BY vehicleid, date;
EOF

  description = "Daily vehicle health summary"
}

resource "aws_athena_named_query" "maintenance_alerts" {
  name      = "maintenance_alerts"
  workgroup = aws_athena_workgroup.fleet_analytics.id
  database  = aws_glue_catalog_database.fleet_data_catalog.name

  query = <<EOF
SELECT 
    vehicleid,
    timestamp,
    vehiclehealth.maintenancescore,
    vehiclehealth.anomalyscore,
    enginediagnostics.enginetemperature,
    enginediagnostics.oilpressure,
    CASE 
        WHEN vehiclehealth.maintenancescore < 30 THEN 'CRITICAL'
        WHEN vehiclehealth.maintenancescore < 50 THEN 'HIGH'
        WHEN vehiclehealth.maintenancescore < 70 THEN 'MEDIUM'
        ELSE 'LOW'
    END as maintenance_priority
FROM vehicle_telemetry
WHERE vehiclehealth.maintenancescore < 70
    OR vehiclehealth.anomalyscore > 50
    OR enginediagnostics.enginetemperature > 100
    OR enginediagnostics.oilpressure < 25
ORDER BY vehiclehealth.maintenancescore ASC, timestamp DESC;
EOF

  description = "Vehicles requiring maintenance attention"
}
