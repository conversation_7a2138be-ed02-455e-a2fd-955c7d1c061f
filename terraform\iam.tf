# IAM Roles and Policies for Fleet Management Digital Twins

# TwinMaker Execution Role
resource "aws_iam_role" "twinmaker_execution_role" {
  name = "TwinMakerExecutionRole"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = [
            "iottwinmaker.amazonaws.com",
            "lambda.amazonaws.com"
          ]
        }
      }
    ]
  })

  tags = merge(local.common_tags, {
    Name = "TwinMaker Execution Role"
  })
}

resource "aws_iam_role_policy_attachment" "twinmaker_service_role" {
  role       = aws_iam_role.twinmaker_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/AWSIoTTwinMakerServiceRolePolicy"
}

resource "aws_iam_role_policy" "twinmaker_s3_access" {
  name = "TwinMakerS3Access"
  role = aws_iam_role.twinmaker_execution_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject",
          "s3:ListBucket"
        ]
        Resource = [
          aws_s3_bucket.twinmaker_workspace.arn,
          "${aws_s3_bucket.twinmaker_workspace.arn}/*"
        ]
      }
    ]
  })
}

resource "aws_iam_role_policy" "twinmaker_timestream_access" {
  name = "TwinMakerTimestreamAccess"
  role = aws_iam_role.twinmaker_execution_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "timestream:Select",
          "timestream:DescribeTable",
          "timestream:ListTables",
          "timestream:DescribeDatabase",
          "timestream:ListDatabases"
        ]
        Resource = "*"
      }
    ]
  })
}

# Lambda Execution Role
resource "aws_iam_role" "lambda_execution_role" {
  name = "FleetLambdaExecutionRole"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })

  tags = merge(local.common_tags, {
    Name = "Lambda Execution Role"
  })
}

resource "aws_iam_role_policy_attachment" "lambda_basic_execution" {
  role       = aws_iam_role.lambda_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

resource "aws_iam_role_policy" "lambda_sitewise_access" {
  name = "LambdaSiteWiseAccess"
  role = aws_iam_role.lambda_execution_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "iotsitewise:BatchPutAssetPropertyValue",
          "iotsitewise:GetAssetPropertyValue",
          "iotsitewise:GetAssetPropertyValueHistory",
          "iotsitewise:GetAssetPropertyAggregates",
          "iotsitewise:ListAssets",
          "iotsitewise:DescribeAsset",
          "iotsitewise:DescribeAssetModel",
          "iotsitewise:ListAssetModels",
          "iotsitewise:UpdateAssetProperty",
          "iotsitewise:CreateAsset",
          "iotsitewise:UpdateAsset",
          "iotsitewise:DeleteAsset"
        ]
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_role_policy" "lambda_iot_access" {
  name = "LambdaIoTAccess"
  role = aws_iam_role.lambda_execution_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "iot:Publish",
          "iot:Subscribe",
          "iot:Connect",
          "iot:Receive",
          "iot:GetThingShadow",
          "iot:UpdateThingShadow",
          "iot:DeleteThingShadow"
        ]
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_role_policy" "lambda_s3_access" {
  name = "LambdaS3Access"
  role = aws_iam_role.lambda_execution_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject",
          "s3:ListBucket"
        ]
        Resource = [
          aws_s3_bucket.data_lake.arn,
          "${aws_s3_bucket.data_lake.arn}/*",
          aws_s3_bucket.lambda_deployments.arn,
          "${aws_s3_bucket.lambda_deployments.arn}/*"
        ]
      }
    ]
  })
}

resource "aws_iam_role_policy" "lambda_sagemaker_access" {
  name = "LambdaSageMakerAccess"
  role = aws_iam_role.lambda_execution_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "sagemaker:InvokeEndpoint"
        ]
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_role_policy" "lambda_twinmaker_access" {
  name = "LambdaTwinMakerAccess"
  role = aws_iam_role.lambda_execution_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "iottwinmaker:UpdateEntity",
          "iottwinmaker:GetEntity",
          "iottwinmaker:UpdatePropertyValues",
          "iottwinmaker:GetPropertyValue",
          "iottwinmaker:GetPropertyValueHistory"
        ]
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_role_policy" "lambda_sns_access" {
  name = "LambdaSNSAccess"
  role = aws_iam_role.lambda_execution_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "sns:Publish"
        ]
        Resource = [
          aws_sns_topic.maintenance_alerts.arn,
          aws_sns_topic.anomaly_alerts.arn
        ]
      }
    ]
  })
}

# IoT Rule Execution Role
resource "aws_iam_role" "iot_rule_role" {
  name = "IoTRuleExecutionRole"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "iot.amazonaws.com"
        }
      }
    ]
  })

  tags = merge(local.common_tags, {
    Name = "IoT Rule Execution Role"
  })
}

resource "aws_iam_role_policy" "iot_rule_sitewise_access" {
  name = "IoTRuleSiteWiseAccess"
  role = aws_iam_role.iot_rule_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "iotsitewise:BatchPutAssetPropertyValue",
          "iotsitewise:GetAssetPropertyValue",
          "iotsitewise:DescribeAsset",
          "iotsitewise:DescribeAssetModel",
          "iotsitewise:ListAssets",
          "iotsitewise:ListAssetModels"
        ]
        Resource = "*"
      }
    ]
  })
}

# SageMaker Execution Role
resource "aws_iam_role" "sagemaker_execution_role" {
  name = "SageMakerExecutionRole"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "sagemaker.amazonaws.com"
        }
      }
    ]
  })

  tags = merge(local.common_tags, {
    Name = "SageMaker Execution Role"
  })
}

resource "aws_iam_role_policy_attachment" "sagemaker_full_access" {
  role       = aws_iam_role.sagemaker_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSageMakerFullAccess"
}

resource "aws_iam_role_policy" "sagemaker_s3_access" {
  name = "SageMakerS3Access"
  role = aws_iam_role.sagemaker_execution_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject",
          "s3:ListBucket"
        ]
        Resource = [
          aws_s3_bucket.data_lake.arn,
          "${aws_s3_bucket.data_lake.arn}/*"
        ]
      }
    ]
  })
}

# Kinesis Firehose Delivery Role
resource "aws_iam_role" "firehose_delivery_role" {
  name = "FirehoseDeliveryRole"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "firehose.amazonaws.com"
        }
      }
    ]
  })

  tags = merge(local.common_tags, {
    Name = "Firehose Delivery Role"
  })
}

resource "aws_iam_role_policy" "firehose_delivery_policy" {
  name = "FirehoseDeliveryPolicy"
  role = aws_iam_role.firehose_delivery_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:AbortMultipartUpload",
          "s3:GetBucketLocation",
          "s3:GetObject",
          "s3:ListBucket",
          "s3:ListBucketMultipartUploads",
          "s3:PutObject"
        ]
        Resource = [
          aws_s3_bucket.data_lake.arn,
          "${aws_s3_bucket.data_lake.arn}/*"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "kinesis:DescribeStream",
          "kinesis:GetShardIterator",
          "kinesis:GetRecords"
        ]
        Resource = [
          aws_kinesis_stream.vehicle_telemetry.arn,
          aws_kinesis_stream.driver_behavior.arn,
          aws_kinesis_stream.charging_data.arn
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "lambda:InvokeFunction",
          "lambda:GetFunctionConfiguration"
        ]
        Resource = "arn:aws:lambda:${local.region}:${local.account_id}:function:fleet-*"
      }
    ]
  })
}
