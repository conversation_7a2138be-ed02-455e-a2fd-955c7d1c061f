# AWS IoT SiteWise Configuration for Fleet Management

# IoT SiteWise Gateway (for edge data collection)
resource "aws_iotsitewise_gateway" "fleet_gateway" {
  gateway_name = "fleet-management-gateway"

  gateway_platform {
    greengrass {
      group_arn = aws_greengrass_group.fleet_gateway_group.arn
    }
  }

  tags = merge(local.common_tags, {
    Name = "Fleet Management Gateway"
  })
}

# Greengrass Group for SiteWise Gateway
resource "aws_greengrass_group" "fleet_gateway_group" {
  name = "fleet-sitewise-gateway-group"

  initial_version {
    core_definition_version_arn = aws_greengrass_core_definition_version.fleet_core.arn
    
    function_definition_version_arn = aws_greengrass_function_definition_version.sitewise_collector.arn
    
    subscription_definition_version_arn = aws_greengrass_subscription_definition_version.fleet_subscriptions.arn
  }

  tags = merge(local.common_tags, {
    Name = "Fleet Gateway Greengrass Group"
  })
}

# Greengrass Core Definition
resource "aws_greengrass_core_definition" "fleet_core" {
  name = "fleet-gateway-core"

  tags = merge(local.common_tags, {
    Name = "Fleet Gateway Core"
  })
}

resource "aws_greengrass_core_definition_version" "fleet_core" {
  core_definition_id = aws_greengrass_core_definition.fleet_core.id

  cores {
    id             = "fleet-gateway-core-1"
    certificate_arn = aws_iot_certificate.gateway_cert.arn
    thing_arn      = aws_iot_thing.gateway_thing.arn
    sync_shadow    = true
  }
}

# IoT Thing for Gateway
resource "aws_iot_thing" "gateway_thing" {
  name = "fleet-gateway-thing"
  
  thing_type_name = aws_iot_thing_type.gateway_type.name

  tags = merge(local.common_tags, {
    Name = "Fleet Gateway Thing"
  })
}

resource "aws_iot_thing_type" "gateway_type" {
  name = "FleetGateway"

  properties {
    description = "Fleet management gateway thing type"
    searchable_attributes = [
      "gatewayType",
      "location"
    ]
  }

  tags = merge(local.common_tags, {
    Name = "Fleet Gateway Thing Type"
  })
}

# IoT Certificate for Gateway
resource "aws_iot_certificate" "gateway_cert" {
  active = true

  tags = merge(local.common_tags, {
    Name = "Fleet Gateway Certificate"
  })
}

# Attach policy to gateway certificate
resource "aws_iot_policy_attachment" "gateway_policy_attachment" {
  policy = aws_iot_policy.gateway_policy.name
  target = aws_iot_certificate.gateway_cert.arn
}

# Gateway IoT Policy
resource "aws_iot_policy" "gateway_policy" {
  name = "FleetGatewayPolicy"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "iot:Connect",
          "iot:Publish",
          "iot:Subscribe",
          "iot:Receive",
          "greengrass:*"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "iotsitewise:BatchPutAssetPropertyValue",
          "iotsitewise:GetAssetPropertyValue",
          "iotsitewise:GetAssetPropertyValueHistory"
        ]
        Resource = "*"
      }
    ]
  })
}

# Greengrass Function Definition for SiteWise Collector
resource "aws_greengrass_function_definition" "sitewise_collector" {
  name = "sitewise-collector-function"

  tags = merge(local.common_tags, {
    Name = "SiteWise Collector Function"
  })
}

resource "aws_greengrass_function_definition_version" "sitewise_collector" {
  function_definition_id = aws_greengrass_function_definition.sitewise_collector.id

  functions {
    id = "sitewise-collector"
    function_arn = aws_lambda_function.sitewise_collector.arn
    function_configuration {
      pinned = true
      timeout = 25000
      memory_size = 128000
      encoding_type = "json"
      
      environment {
        access_sysfs = false
        resource_access_policies {
          resource_id = "sitewise-data"
          permission = "rw"
        }
      }
    }
  }
}

# Lambda Function for SiteWise Data Collection
resource "aws_lambda_function" "sitewise_collector" {
  function_name = "fleet-sitewise-collector"
  role         = aws_iam_role.sitewise_collector_role.arn
  handler      = "lambda_function.lambda_handler"
  runtime      = var.lambda_runtime
  timeout      = 60
  memory_size  = 256

  filename = "${path.module}/lambda-packages/sitewise-collector.zip"

  environment {
    variables = {
      SITEWISE_GATEWAY_ID = aws_iotsitewise_gateway.fleet_gateway.id
      FLEET_ASSET_MODEL_ID = aws_iotsitewise_asset_model.fleet_model.id
      VEHICLE_ASSET_MODEL_ID = aws_iotsitewise_asset_model.fleet_vehicle_model.id
      EV_ASSET_MODEL_ID = aws_iotsitewise_asset_model.electric_vehicle_model.id
      CHARGING_STATION_MODEL_ID = aws_iotsitewise_asset_model.charging_station_model.id
    }
  }

  tags = merge(local.common_tags, {
    Name = "SiteWise Collector Function"
  })
}

# IAM Role for SiteWise Collector
resource "aws_iam_role" "sitewise_collector_role" {
  name = "SiteWiseCollectorRole"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = [
            "lambda.amazonaws.com",
            "greengrass.amazonaws.com"
          ]
        }
      }
    ]
  })

  tags = merge(local.common_tags, {
    Name = "SiteWise Collector Role"
  })
}

resource "aws_iam_role_policy" "sitewise_collector_policy" {
  name = "SiteWiseCollectorPolicy"
  role = aws_iam_role.sitewise_collector_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:*:*:*"
      },
      {
        Effect = "Allow"
        Action = [
          "iotsitewise:BatchPutAssetPropertyValue",
          "iotsitewise:GetAssetPropertyValue",
          "iotsitewise:GetAssetPropertyValueHistory",
          "iotsitewise:ListAssets",
          "iotsitewise:DescribeAsset",
          "iotsitewise:DescribeAssetModel",
          "iotsitewise:ListAssetModels"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "greengrass:GetConnectivityInfo",
          "greengrass:UpdateConnectivityInfo"
        ]
        Resource = "*"
      }
    ]
  })
}

# Greengrass Subscription Definition
resource "aws_greengrass_subscription_definition" "fleet_subscriptions" {
  name = "fleet-subscriptions"

  tags = merge(local.common_tags, {
    Name = "Fleet Subscriptions"
  })
}

resource "aws_greengrass_subscription_definition_version" "fleet_subscriptions" {
  subscription_definition_id = aws_greengrass_subscription_definition.fleet_subscriptions.id

  subscriptions {
    id      = "fleet-telemetry-subscription"
    source  = "arn:aws:iot:${local.region}:${local.account_id}:topic/fleet/+/telemetry"
    subject = aws_lambda_function.sitewise_collector.arn
    target  = aws_lambda_function.sitewise_collector.arn
  }

  subscriptions {
    id      = "charging-station-subscription"
    source  = "arn:aws:iot:${local.region}:${local.account_id}:topic/fleet/charging-stations/+/status"
    subject = aws_lambda_function.sitewise_collector.arn
    target  = aws_lambda_function.sitewise_collector.arn
  }
}

# IoT SiteWise Portal
resource "aws_iotsitewise_portal" "fleet_portal" {
  portal_name         = "fleet-management-portal"
  portal_description  = "Fleet Management Digital Twins Portal"
  portal_contact_email = var.notification_email != "" ? var.notification_email : "<EMAIL>"
  role_arn           = aws_iam_role.sitewise_portal_role.arn

  portal_logo_image_file {
    data = filebase64("${path.module}/../assets/fleet-logo.png")
    type = "image/png"
  }

  tags = merge(local.common_tags, {
    Name = "Fleet Management Portal"
  })
}

# IAM Role for SiteWise Portal
resource "aws_iam_role" "sitewise_portal_role" {
  name = "SiteWisePortalRole"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "iotsitewise.amazonaws.com"
        }
      }
    ]
  })

  tags = merge(local.common_tags, {
    Name = "SiteWise Portal Role"
  })
}

resource "aws_iam_role_policy_attachment" "sitewise_portal_policy" {
  role       = aws_iam_role.sitewise_portal_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSIoTSiteWiseMonitorPortalAccess"
}

# SiteWise Project
resource "aws_iotsitewise_project" "fleet_project" {
  project_name        = "fleet-management-project"
  project_description = "Fleet Management Digital Twins Project"
  portal_id          = aws_iotsitewise_portal.fleet_portal.id

  tags = merge(local.common_tags, {
    Name = "Fleet Management Project"
  })
}

# SiteWise Dashboard
resource "aws_iotsitewise_dashboard" "fleet_overview" {
  project_id          = aws_iotsitewise_project.fleet_project.id
  dashboard_name      = "fleet-overview-dashboard"
  dashboard_description = "Fleet Overview Dashboard"

  dashboard_definition = jsonencode({
    widgets = [
      {
        type = "sc-line-chart"
        title = "Vehicle Engine Temperature"
        properties = {
          metrics = [
            {
              label = "Engine Temperature"
              assetId = "ASSET_ID_PLACEHOLDER"
              propertyId = "ENGINE_TEMPERATURE_PROPERTY_ID"
            }
          ]
          viewport = {
            duration = "1h"
          }
        }
      },
      {
        type = "sc-status-grid"
        title = "Fleet Status"
        properties = {
          metrics = [
            {
              label = "Maintenance Score"
              assetId = "ASSET_ID_PLACEHOLDER"
              propertyId = "MAINTENANCE_SCORE_PROPERTY_ID"
            }
          ]
        }
      },
      {
        type = "sc-kpi"
        title = "Fleet Utilization"
        properties = {
          metrics = [
            {
              label = "Utilization Rate"
              assetId = "FLEET_ASSET_ID_PLACEHOLDER"
              propertyId = "FLEET_UTILIZATION_PROPERTY_ID"
            }
          ]
        }
      }
    ]
  })

  tags = merge(local.common_tags, {
    Name = "Fleet Overview Dashboard"
  })
}
