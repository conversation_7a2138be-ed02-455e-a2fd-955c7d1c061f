# IoT Core Resources for Fleet Management Digital Twins

# IoT Thing Types
resource "aws_iot_thing_type" "fleet_vehicle" {
  name = "FleetVehicle"

  properties {
    description = "Fleet vehicle IoT thing type"
    searchable_attributes = [
      "vehicleType",
      "make",
      "model",
      "year"
    ]
  }

  tags = merge(local.common_tags, {
    Name = "Fleet Vehicle Thing Type"
  })
}

resource "aws_iot_thing_type" "charging_station" {
  name = "ChargingStation"

  properties {
    description = "EV charging station IoT thing type"
    searchable_attributes = [
      "stationType",
      "maxPower",
      "connectors"
    ]
  }

  tags = merge(local.common_tags, {
    Name = "Charging Station Thing Type"
  })
}

# IoT Policy for Fleet Devices
resource "aws_iot_policy" "fleet_device_policy" {
  name = "FleetDevicePolicy"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "iot:Connect"
        ]
        Resource = [
          "arn:aws:iot:${local.region}:${local.account_id}:client/$${iot:Connection.Thing.ThingName}"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "iot:Publish"
        ]
        Resource = [
          "arn:aws:iot:${local.region}:${local.account_id}:topic/fleet/vehicles/$${iot:Connection.Thing.ThingName}/telemetry",
          "arn:aws:iot:${local.region}:${local.account_id}:topic/fleet/vehicles/$${iot:Connection.Thing.ThingName}/diagnostics",
          "arn:aws:iot:${local.region}:${local.account_id}:topic/fleet/vehicles/$${iot:Connection.Thing.ThingName}/location",
          "arn:aws:iot:${local.region}:${local.account_id}:topic/fleet/vehicles/$${iot:Connection.Thing.ThingName}/battery",
          "arn:aws:iot:${local.region}:${local.account_id}:topic/fleet/vehicles/$${iot:Connection.Thing.ThingName}/charging",
          "arn:aws:iot:${local.region}:${local.account_id}:topic/fleet/drivers/$${iot:Connection.Thing.ThingName}/behavior",
          "arn:aws:iot:${local.region}:${local.account_id}:topic/fleet/charging-stations/$${iot:Connection.Thing.ThingName}/status",
          "arn:aws:iot:${local.region}:${local.account_id}:topic/fleet/charging-stations/$${iot:Connection.Thing.ThingName}/usage"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "iot:Subscribe"
        ]
        Resource = [
          "arn:aws:iot:${local.region}:${local.account_id}:topicfilter/fleet/vehicles/$${iot:Connection.Thing.ThingName}/commands/*",
          "arn:aws:iot:${local.region}:${local.account_id}:topicfilter/fleet/drivers/$${iot:Connection.Thing.ThingName}/commands/*",
          "arn:aws:iot:${local.region}:${local.account_id}:topicfilter/fleet/charging-stations/$${iot:Connection.Thing.ThingName}/commands/*",
          "arn:aws:iot:${local.region}:${local.account_id}:topicfilter/fleet/optimization/*",
          "arn:aws:iot:${local.region}:${local.account_id}:topicfilter/fleet/maintenance/*",
          "arn:aws:iot:${local.region}:${local.account_id}:topicfilter/fleet/alerts/*"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "iot:Receive"
        ]
        Resource = [
          "arn:aws:iot:${local.region}:${local.account_id}:topic/fleet/vehicles/$${iot:Connection.Thing.ThingName}/commands/*",
          "arn:aws:iot:${local.region}:${local.account_id}:topic/fleet/drivers/$${iot:Connection.Thing.ThingName}/commands/*",
          "arn:aws:iot:${local.region}:${local.account_id}:topic/fleet/charging-stations/$${iot:Connection.Thing.ThingName}/commands/*",
          "arn:aws:iot:${local.region}:${local.account_id}:topic/fleet/optimization/*",
          "arn:aws:iot:${local.region}:${local.account_id}:topic/fleet/maintenance/*",
          "arn:aws:iot:${local.region}:${local.account_id}:topic/fleet/alerts/*"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "iot:UpdateThingShadow",
          "iot:GetThingShadow"
        ]
        Resource = [
          "arn:aws:iot:${local.region}:${local.account_id}:thing/$${iot:Connection.Thing.ThingName}"
        ]
      }
    ]
  })
}

# IoT Topic Rules for routing data to SiteWise
resource "aws_iot_topic_rule" "vehicle_telemetry_rule" {
  name        = "FleetVehicleTelemetryRule"
  description = "Route vehicle telemetry data to IoT SiteWise"
  enabled     = true
  sql         = "SELECT *, timestamp() as timestamp FROM 'fleet/vehicles/+/telemetry'"
  sql_version = "2016-03-23"

  iot_site_wise {
    role_arn = aws_iam_role.iot_rule_role.arn

    put_asset_property_value_entries {
      asset_id     = "$${assetId}"
      property_id  = "$${propertyId}"

      property_values {
        value {
          string_value = "$${engineDiagnostics.engineTemperature}"
        }
        timestamp {
          time_in_seconds = "$${timestamp}"
        }
        quality = "GOOD"
      }
    }
  }

  tags = merge(local.common_tags, {
    Name = "Vehicle Telemetry Rule"
  })
}

resource "aws_iot_topic_rule" "charging_station_rule" {
  name        = "FleetChargingStationRule"
  description = "Route charging station data to IoT SiteWise"
  enabled     = true
  sql         = "SELECT *, timestamp() as timestamp FROM 'fleet/charging-stations/+/status'"
  sql_version = "2016-03-23"

  iot_site_wise {
    role_arn = aws_iam_role.iot_rule_role.arn

    put_asset_property_value_entries {
      asset_id     = "$${stationAssetId}"
      property_id  = "$${propertyId}"

      property_values {
        value {
          double_value = "$${stationStatus.currentPower}"
        }
        timestamp {
          time_in_seconds = "$${timestamp}"
        }
        quality = "GOOD"
      }
    }
  }

  tags = merge(local.common_tags, {
    Name = "Charging Station Rule"
  })
}

resource "aws_iot_topic_rule" "ev_battery_rule" {
  name        = "FleetEVBatteryRule"
  description = "Route EV battery data to IoT SiteWise"
  enabled     = true
  sql         = "SELECT *, timestamp() as timestamp FROM 'fleet/vehicles/+/battery'"
  sql_version = "2016-03-23"

  iot_site_wise {
    role_arn = aws_iam_role.iot_rule_role.arn

    put_asset_property_value_entries {
      asset_id     = "$${vehicleAssetId}"
      property_id  = "$${propertyId}"

      property_values {
        value {
          double_value = "$${batteryStatus.stateOfCharge}"
        }
        timestamp {
          time_in_seconds = "$${timestamp}"
        }
        quality = "GOOD"
      }
    }
  }

  tags = merge(local.common_tags, {
    Name = "EV Battery Rule"
  })
}

# IoT Topic Rule for Error Handling
resource "aws_iot_topic_rule" "error_handling_rule" {
  name        = "FleetErrorHandlingRule"
  description = "Handle IoT rule errors and send to CloudWatch"
  enabled     = true
  sql         = "SELECT * FROM '$aws/rules/+'"
  sql_version = "2016-03-23"

  cloudwatch_logs {
    log_group_name = aws_cloudwatch_log_group.iot_errors.name
    role_arn       = aws_iam_role.iot_rule_role.arn
  }

  tags = merge(local.common_tags, {
    Name = "Error Handling Rule"
  })
}

# CloudWatch Log Group for IoT Errors
resource "aws_cloudwatch_log_group" "iot_errors" {
  name              = "/aws/iot/errors"
  retention_in_days = var.log_retention_days

  tags = merge(local.common_tags, {
    Name = "IoT Errors Log Group"
  })
}

# IoT Device Defender Security Profile
resource "aws_iot_security_profile" "fleet_security_profile" {
  name        = "FleetSecurityProfile"
  description = "Security profile for fleet devices"

  behaviors {
    name   = "num-messages-sent"
    metric = "aws:num-messages-sent"

    criteria {
      comparison_operator = "greater-than"
      value {
        count = 1000
      }
      duration_seconds = 300
    }
  }

  behaviors {
    name   = "num-authorization-failures"
    metric = "aws:num-authorization-failures"

    criteria {
      comparison_operator = "greater-than"
      value {
        count = 5
      }
      duration_seconds = 300
    }
  }

  alert_targets = {
    SNS = {
      alert_target_arn = aws_sns_topic.anomaly_alerts.arn
      role_arn        = aws_iam_role.iot_rule_role.arn
    }
  }

  tags = merge(local.common_tags, {
    Name = "Fleet Security Profile"
  })
}

# IoT Device Defender Detect Target
resource "aws_iot_thing_group" "fleet_vehicles" {
  name = "FleetVehicles"

  thing_group_properties {
    description = "All fleet vehicles"
  }

  tags = merge(local.common_tags, {
    Name = "Fleet Vehicles Thing Group"
  })
}

# Attach security profile to thing group
resource "aws_iot_thing_group_membership" "fleet_security_attachment" {
  thing_group_name = aws_iot_thing_group.fleet_vehicles.name
  thing_name       = aws_iot_thing_group.fleet_vehicles.name
}

# IoT Jobs for device management
resource "aws_iot_job_template" "firmware_update" {
  job_template_id = "fleet-firmware-update"
  description     = "Template for fleet vehicle firmware updates"

  document = jsonencode({
    operation = "firmware_update"
    files = {
      firmware = {
        url = "https://${aws_s3_bucket.data_lake.bucket}.s3.${local.region}.amazonaws.com/firmware/"
      }
    }
    steps = [
      {
        action = "download"
        handler = "download_firmware"
      },
      {
        action = "install"
        handler = "install_firmware"
      },
      {
        action = "verify"
        handler = "verify_installation"
      }
    ]
  })

  job_executions_rollout_config {
    maximum_per_minute = 10
  }

  abort_config {
    criteria_list {
      action          = "CANCEL"
      failure_type    = "FAILED"
      min_number_of_executed_things = 1
      threshold_percentage = 10.0
    }
  }

  timeout_config {
    in_progress_timeout_in_minutes = 60
  }

  tags = merge(local.common_tags, {
    Name = "Firmware Update Job Template"
  })
}

# IoT Fleet Indexing
resource "aws_iot_thing_group_info" "fleet_indexing" {
  thing_indexing_configuration {
    thing_indexing_mode = "REGISTRY_AND_SHADOW"

    thing_connectivity_indexing_mode = "STATUS"

    device_defender_indexing_mode = "VIOLATIONS"

    named_shadow_indexing_configuration_list {
      shadow_name                   = "fleet-config"
      thing_group_indexing_mode    = "ON"
    }
  }

  thing_group_indexing_configuration {
    thing_group_indexing_mode = "ON"
  }
}
