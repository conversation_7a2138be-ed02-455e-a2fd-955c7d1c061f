# Kinesis Firehose Delivery Streams for Data Lake

# Kinesis Firehose for Vehicle Telemetry
resource "aws_kinesis_firehose_delivery_stream" "vehicle_telemetry_firehose" {
  name        = "fleet-vehicle-telemetry-firehose"
  destination = "extended_s3"

  kinesis_source_configuration {
    kinesis_stream_arn = aws_kinesis_stream.vehicle_telemetry.arn
    role_arn          = aws_iam_role.firehose_delivery_role.arn
  }

  extended_s3_configuration {
    role_arn           = aws_iam_role.firehose_delivery_role.arn
    bucket_arn         = aws_s3_bucket.data_lake.arn
    prefix             = "telemetry/vehicle/year=!{timestamp:yyyy}/month=!{timestamp:MM}/day=!{timestamp:dd}/hour=!{timestamp:HH}/"
    error_output_prefix = "errors/vehicle-telemetry/"
    
    buffer_size     = var.kinesis_firehose_buffer_size
    buffer_interval = var.kinesis_firehose_buffer_interval
    
    compression_format = "GZIP"
    
    processing_configuration {
      enabled = true
      
      processors {
        type = "Lambda"
        
        parameters {
          parameter_name  = "LambdaArn"
          parameter_value = aws_lambda_function.telemetry_processor.arn
        }
      }
    }

    data_format_conversion_configuration {
      enabled = true
      
      output_format_configuration {
        serializer {
          parquet_ser_de {}
        }
      }
      
      schema_configuration {
        database_name = aws_glue_catalog_database.fleet_data_catalog.name
        table_name    = aws_glue_catalog_table.vehicle_telemetry_table.name
        role_arn      = aws_iam_role.firehose_delivery_role.arn
      }
    }

    cloudwatch_logging_options {
      enabled         = true
      log_group_name  = aws_cloudwatch_log_group.firehose_logs.name
      log_stream_name = "vehicle-telemetry"
    }
  }

  tags = merge(local.common_tags, {
    Name = "Vehicle Telemetry Firehose"
  })
}

# Kinesis Firehose for Driver Behavior
resource "aws_kinesis_firehose_delivery_stream" "driver_behavior_firehose" {
  name        = "fleet-driver-behavior-firehose"
  destination = "extended_s3"

  kinesis_source_configuration {
    kinesis_stream_arn = aws_kinesis_stream.driver_behavior.arn
    role_arn          = aws_iam_role.firehose_delivery_role.arn
  }

  extended_s3_configuration {
    role_arn           = aws_iam_role.firehose_delivery_role.arn
    bucket_arn         = aws_s3_bucket.data_lake.arn
    prefix             = "telemetry/driver/year=!{timestamp:yyyy}/month=!{timestamp:MM}/day=!{timestamp:dd}/hour=!{timestamp:HH}/"
    error_output_prefix = "errors/driver-behavior/"
    
    buffer_size     = var.kinesis_firehose_buffer_size
    buffer_interval = var.kinesis_firehose_buffer_interval
    
    compression_format = "GZIP"

    cloudwatch_logging_options {
      enabled         = true
      log_group_name  = aws_cloudwatch_log_group.firehose_logs.name
      log_stream_name = "driver-behavior"
    }
  }

  tags = merge(local.common_tags, {
    Name = "Driver Behavior Firehose"
  })
}

# Kinesis Firehose for Charging Data
resource "aws_kinesis_firehose_delivery_stream" "charging_data_firehose" {
  name        = "fleet-charging-data-firehose"
  destination = "extended_s3"

  kinesis_source_configuration {
    kinesis_stream_arn = aws_kinesis_stream.charging_data.arn
    role_arn          = aws_iam_role.firehose_delivery_role.arn
  }

  extended_s3_configuration {
    role_arn           = aws_iam_role.firehose_delivery_role.arn
    bucket_arn         = aws_s3_bucket.data_lake.arn
    prefix             = "telemetry/charging/year=!{timestamp:yyyy}/month=!{timestamp:MM}/day=!{timestamp:dd}/hour=!{timestamp:HH}/"
    error_output_prefix = "errors/charging-data/"
    
    buffer_size     = var.kinesis_firehose_buffer_size
    buffer_interval = var.kinesis_firehose_buffer_interval
    
    compression_format = "GZIP"

    cloudwatch_logging_options {
      enabled         = true
      log_group_name  = aws_cloudwatch_log_group.firehose_logs.name
      log_stream_name = "charging-data"
    }
  }

  tags = merge(local.common_tags, {
    Name = "Charging Data Firehose"
  })
}

# CloudWatch Log Group for Firehose
resource "aws_cloudwatch_log_group" "firehose_logs" {
  name              = "/aws/kinesisfirehose/fleet-management"
  retention_in_days = var.log_retention_days

  tags = merge(local.common_tags, {
    Name = "Firehose Log Group"
  })
}

# Kinesis Analytics Application for Real-time Processing
resource "aws_kinesis_analytics_application" "fleet_analytics" {
  name = "fleet-real-time-analytics"

  application_code = <<EOF
CREATE OR REPLACE STREAM "DESTINATION_SQL_STREAM" (
    vehicleId VARCHAR(32),
    timestamp TIMESTAMP,
    anomaly_score DOUBLE,
    maintenance_score DOUBLE,
    alert_level VARCHAR(16)
);

CREATE OR REPLACE PUMP "STREAM_PUMP" AS INSERT INTO "DESTINATION_SQL_STREAM"
SELECT STREAM 
    vehicleId,
    ROWTIME_TO_TIMESTAMP(ROWTIME) as timestamp,
    CASE 
        WHEN engineTemperature > 110 OR oilPressure < 20 THEN 0.8
        WHEN engineTemperature > 100 OR oilPressure < 25 THEN 0.6
        WHEN engineTemperature > 95 OR oilPressure < 30 THEN 0.4
        ELSE 0.2
    END as anomaly_score,
    CASE 
        WHEN odometer % 10000 < 100 THEN 0.3
        WHEN brakeWearLevel < 30 THEN 0.4
        WHEN batteryVoltage < 11.5 THEN 0.5
        ELSE 0.8
    END as maintenance_score,
    CASE 
        WHEN engineTemperature > 110 OR oilPressure < 20 THEN 'CRITICAL'
        WHEN engineTemperature > 100 OR oilPressure < 25 THEN 'HIGH'
        WHEN engineTemperature > 95 OR oilPressure < 30 THEN 'MEDIUM'
        ELSE 'LOW'
    END as alert_level
FROM "SOURCE_SQL_STREAM_001"
WHERE vehicleId IS NOT NULL;
EOF

  inputs {
    name_prefix = "SOURCE_SQL_STREAM"
    
    input_schema {
      record_columns {
        name     = "vehicleId"
        sql_type = "VARCHAR(32)"
        mapping  = "$.vehicleId"
      }
      
      record_columns {
        name     = "engineTemperature"
        sql_type = "DOUBLE"
        mapping  = "$.engineDiagnostics.engineTemperature"
      }
      
      record_columns {
        name     = "oilPressure"
        sql_type = "DOUBLE"
        mapping  = "$.engineDiagnostics.oilPressure"
      }
      
      record_columns {
        name     = "odometer"
        sql_type = "DOUBLE"
        mapping  = "$.vehicleHealth.odometer"
      }
      
      record_columns {
        name     = "brakeWearLevel"
        sql_type = "DOUBLE"
        mapping  = "$.vehicleHealth.brakeWearLevel"
      }
      
      record_columns {
        name     = "batteryVoltage"
        sql_type = "DOUBLE"
        mapping  = "$.vehicleHealth.batteryVoltage"
      }

      record_format {
        record_format_type = "JSON"
        
        mapping_parameters {
          json_mapping_parameters {
            record_row_path = "$"
          }
        }
      }
    }
    
    kinesis_stream {
      resource_arn = aws_kinesis_stream.vehicle_telemetry.arn
      role_arn     = aws_iam_role.kinesis_analytics_role.arn
    }
  }

  outputs {
    name = "DESTINATION_SQL_STREAM"
    
    destination_schema {
      record_format_type = "JSON"
    }
    
    kinesis_stream {
      resource_arn = aws_kinesis_stream.analytics_output.arn
      role_arn     = aws_iam_role.kinesis_analytics_role.arn
    }
  }

  tags = merge(local.common_tags, {
    Name = "Fleet Analytics Application"
  })
}

# Output stream for analytics results
resource "aws_kinesis_stream" "analytics_output" {
  name             = "fleet-analytics-output"
  shard_count      = 1
  retention_period = 24

  encryption_type = "KMS"
  kms_key_id      = "alias/aws/kinesis"

  tags = merge(local.common_tags, {
    Name = "Analytics Output Stream"
  })
}

# IAM Role for Kinesis Analytics
resource "aws_iam_role" "kinesis_analytics_role" {
  name = "KinesisAnalyticsRole"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "kinesisanalytics.amazonaws.com"
        }
      }
    ]
  })

  tags = merge(local.common_tags, {
    Name = "Kinesis Analytics Role"
  })
}

resource "aws_iam_role_policy" "kinesis_analytics_policy" {
  name = "KinesisAnalyticsPolicy"
  role = aws_iam_role.kinesis_analytics_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "kinesis:DescribeStream",
          "kinesis:GetShardIterator",
          "kinesis:GetRecords",
          "kinesis:PutRecord",
          "kinesis:PutRecords"
        ]
        Resource = [
          aws_kinesis_stream.vehicle_telemetry.arn,
          aws_kinesis_stream.analytics_output.arn
        ]
      }
    ]
  })
}

# Kinesis Scaling Configuration
resource "aws_appautoscaling_target" "kinesis_target" {
  max_capacity       = 10
  min_capacity       = 1
  resource_id        = "stream/${aws_kinesis_stream.vehicle_telemetry.name}"
  scalable_dimension = "kinesis:shard:count"
  service_namespace  = "kinesis"
}

resource "aws_appautoscaling_policy" "kinesis_scale_up" {
  name               = "kinesis-scale-up"
  policy_type        = "TargetTrackingScaling"
  resource_id        = aws_appautoscaling_target.kinesis_target.resource_id
  scalable_dimension = aws_appautoscaling_target.kinesis_target.scalable_dimension
  service_namespace  = aws_appautoscaling_target.kinesis_target.service_namespace

  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "KinesisStreamIncomingRecords"
    }
    target_value = 1000.0
  }
}
