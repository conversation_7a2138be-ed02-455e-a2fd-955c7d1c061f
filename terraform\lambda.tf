# Lambda Functions for Fleet Management Digital Twins

# Archive Lambda function code
data "archive_file" "telemetry_processor" {
  type        = "zip"
  source_dir  = "${path.module}/../aws-lambda/telemetry-processor"
  output_path = "${path.module}/lambda-packages/telemetry-processor.zip"
}

data "archive_file" "predictive_maintenance" {
  type        = "zip"
  source_dir  = "${path.module}/../aws-lambda/predictive-maintenance"
  output_path = "${path.module}/lambda-packages/predictive-maintenance.zip"
}

data "archive_file" "anomaly_detection" {
  type        = "zip"
  source_dir  = "${path.module}/../aws-lambda/anomaly-detection"
  output_path = "${path.module}/lambda-packages/anomaly-detection.zip"
}

# Upload Lambda packages to S3
resource "aws_s3_object" "telemetry_processor_package" {
  bucket = aws_s3_bucket.lambda_deployments.bucket
  key    = "lambda-packages/telemetry-processor.zip"
  source = data.archive_file.telemetry_processor.output_path
  etag   = data.archive_file.telemetry_processor.output_md5

  tags = merge(local.common_tags, {
    Name = "Telemetry Processor Package"
  })
}

resource "aws_s3_object" "predictive_maintenance_package" {
  bucket = aws_s3_bucket.lambda_deployments.bucket
  key    = "lambda-packages/predictive-maintenance.zip"
  source = data.archive_file.predictive_maintenance.output_path
  etag   = data.archive_file.predictive_maintenance.output_md5

  tags = merge(local.common_tags, {
    Name = "Predictive Maintenance Package"
  })
}

resource "aws_s3_object" "anomaly_detection_package" {
  bucket = aws_s3_bucket.lambda_deployments.bucket
  key    = "lambda-packages/anomaly-detection.zip"
  source = data.archive_file.anomaly_detection.output_path
  etag   = data.archive_file.anomaly_detection.output_md5

  tags = merge(local.common_tags, {
    Name = "Anomaly Detection Package"
  })
}

# Telemetry Processor Lambda Function
resource "aws_lambda_function" "telemetry_processor" {
  function_name = "fleet-telemetry-processor"
  role         = aws_iam_role.lambda_execution_role.arn
  handler      = "lambda_function.lambda_handler"
  runtime      = var.lambda_runtime
  timeout      = var.lambda_timeout
  memory_size  = var.lambda_memory_size

  s3_bucket = aws_s3_bucket.lambda_deployments.bucket
  s3_key    = aws_s3_object.telemetry_processor_package.key

  environment {
    variables = {
      SITEWISE_FLEET_MODEL_ID     = aws_iotsitewise_asset_model.fleet_model.id
      SITEWISE_VEHICLE_MODEL_ID   = aws_iotsitewise_asset_model.fleet_vehicle_model.id
      SITEWISE_EV_MODEL_ID        = aws_iotsitewise_asset_model.electric_vehicle_model.id
      SITEWISE_CHARGING_MODEL_ID  = aws_iotsitewise_asset_model.charging_station_model.id
      TWINMAKER_WORKSPACE         = var.twinmaker_workspace_name
      SITEWISE_GATEWAY_ID         = var.enable_sitewise_edge_gateway ? aws_iotsitewise_gateway.fleet_gateway[0].id : ""
    }
  }

  reserved_concurrent_executions = var.lambda_reserved_concurrency

  dynamic "tracing_config" {
    for_each = var.enable_x_ray_tracing ? [1] : []
    content {
      mode = "Active"
    }
  }

  depends_on = [
    aws_iam_role_policy_attachment.lambda_basic_execution,
    aws_cloudwatch_log_group.lambda_logs
  ]

  tags = merge(local.common_tags, {
    Name = "Telemetry Processor Function"
  })
}

# Predictive Maintenance Lambda Function
resource "aws_lambda_function" "predictive_maintenance" {
  function_name = "fleet-predictive-maintenance"
  role         = aws_iam_role.lambda_execution_role.arn
  handler      = "lambda_function.lambda_handler"
  runtime      = var.lambda_runtime
  timeout      = var.lambda_timeout
  memory_size  = var.lambda_memory_size

  s3_bucket = aws_s3_bucket.lambda_deployments.bucket
  s3_key    = aws_s3_object.predictive_maintenance_package.key

  environment {
    variables = {
      SAGEMAKER_ENDPOINT         = "predictive-maintenance-endpoint"
      SITEWISE_VEHICLE_MODEL_ID  = aws_iotsitewise_asset_model.fleet_vehicle_model.id
      SITEWISE_EV_MODEL_ID       = aws_iotsitewise_asset_model.electric_vehicle_model.id
      TWINMAKER_WORKSPACE        = var.twinmaker_workspace_name
      SNS_TOPIC_ARN             = aws_sns_topic.maintenance_alerts.arn
    }
  }

  reserved_concurrent_executions = var.lambda_reserved_concurrency

  dynamic "tracing_config" {
    for_each = var.enable_x_ray_tracing ? [1] : []
    content {
      mode = "Active"
    }
  }

  depends_on = [
    aws_iam_role_policy_attachment.lambda_basic_execution,
    aws_cloudwatch_log_group.lambda_logs
  ]

  tags = merge(local.common_tags, {
    Name = "Predictive Maintenance Function"
  })
}

# Anomaly Detection Lambda Function
resource "aws_lambda_function" "anomaly_detection" {
  function_name = "fleet-anomaly-detection"
  role         = aws_iam_role.lambda_execution_role.arn
  handler      = "lambda_function.lambda_handler"
  runtime      = var.lambda_runtime
  timeout      = var.lambda_timeout
  memory_size  = var.lambda_memory_size

  s3_bucket = aws_s3_bucket.lambda_deployments.bucket
  s3_key    = aws_s3_object.anomaly_detection_package.key

  environment {
    variables = {
      SITEWISE_VEHICLE_MODEL_ID  = aws_iotsitewise_asset_model.fleet_vehicle_model.id
      SITEWISE_EV_MODEL_ID       = aws_iotsitewise_asset_model.electric_vehicle_model.id
      TWINMAKER_WORKSPACE        = var.twinmaker_workspace_name
      SNS_TOPIC_ARN             = aws_sns_topic.anomaly_alerts.arn
      MODEL_S3_BUCKET           = aws_s3_bucket.data_lake.bucket
      MODEL_S3_KEY              = "models/anomaly-detection-model.pkl"
    }
  }

  reserved_concurrent_executions = var.lambda_reserved_concurrency

  dynamic "tracing_config" {
    for_each = var.enable_x_ray_tracing ? [1] : []
    content {
      mode = "Active"
    }
  }

  depends_on = [
    aws_iam_role_policy_attachment.lambda_basic_execution,
    aws_cloudwatch_log_group.lambda_logs
  ]

  tags = merge(local.common_tags, {
    Name = "Anomaly Detection Function"
  })
}

# IoT Topic Rules to trigger Lambda functions
resource "aws_iot_topic_rule" "telemetry_processor_trigger" {
  name        = "FleetTelemetryProcessorTrigger"
  description = "Trigger telemetry processor Lambda function"
  enabled     = true
  sql         = "SELECT * FROM 'fleet/vehicles/+/telemetry'"
  sql_version = "2016-03-23"

  lambda {
    function_arn = aws_lambda_function.telemetry_processor.arn
  }

  tags = merge(local.common_tags, {
    Name = "Telemetry Processor Trigger"
  })
}

resource "aws_lambda_permission" "allow_iot_telemetry_processor" {
  statement_id  = "AllowExecutionFromIoT"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.telemetry_processor.function_name
  principal     = "iot.amazonaws.com"
  source_arn    = aws_iot_topic_rule.telemetry_processor_trigger.arn
}

resource "aws_iot_topic_rule" "predictive_maintenance_trigger" {
  name        = "FleetPredictiveMaintenanceTrigger"
  description = "Trigger predictive maintenance analysis"
  enabled     = true
  sql         = "SELECT * FROM 'fleet/vehicles/+/telemetry' WHERE engineDiagnostics.engineTemperature > 90 OR engineDiagnostics.oilPressure < 30"
  sql_version = "2016-03-23"

  lambda {
    function_arn = aws_lambda_function.predictive_maintenance.arn
  }

  tags = merge(local.common_tags, {
    Name = "Predictive Maintenance Trigger"
  })
}

resource "aws_lambda_permission" "allow_iot_predictive_maintenance" {
  statement_id  = "AllowExecutionFromIoT"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.predictive_maintenance.function_name
  principal     = "iot.amazonaws.com"
  source_arn    = aws_iot_topic_rule.predictive_maintenance_trigger.arn
}

resource "aws_iot_topic_rule" "anomaly_detection_trigger" {
  name        = "FleetAnomalyDetectionTrigger"
  description = "Trigger anomaly detection analysis"
  enabled     = true
  sql         = "SELECT * FROM 'fleet/vehicles/+/telemetry'"
  sql_version = "2016-03-23"

  lambda {
    function_arn = aws_lambda_function.anomaly_detection.arn
  }

  tags = merge(local.common_tags, {
    Name = "Anomaly Detection Trigger"
  })
}

resource "aws_lambda_permission" "allow_iot_anomaly_detection" {
  statement_id  = "AllowExecutionFromIoT"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.anomaly_detection.function_name
  principal     = "iot.amazonaws.com"
  source_arn    = aws_iot_topic_rule.anomaly_detection_trigger.arn
}

# Lambda Permissions for EventBridge
resource "aws_lambda_permission" "allow_eventbridge_predictive_maintenance" {
  statement_id  = "AllowExecutionFromEventBridge"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.predictive_maintenance.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.daily_analytics.arn
}

# EventBridge Rules for Scheduled Tasks
resource "aws_cloudwatch_event_rule" "daily_analytics" {
  name                = "fleet-daily-analytics"
  description         = "Trigger daily fleet analytics processing"
  schedule_expression = "cron(0 2 * * ? *)"  # Daily at 2 AM UTC

  tags = merge(local.common_tags, {
    Name = "Daily Analytics Rule"
  })
}

resource "aws_cloudwatch_event_target" "daily_analytics_target" {
  rule      = aws_cloudwatch_event_rule.daily_analytics.name
  target_id = "DailyAnalyticsTarget"
  arn       = aws_lambda_function.predictive_maintenance.arn
}

resource "aws_cloudwatch_event_rule" "weekly_model_retrain" {
  name                = "fleet-weekly-model-retrain"
  description         = "Trigger weekly model retraining"
  schedule_expression = "cron(0 1 ? * MON *)"  # Weekly on Monday at 1 AM UTC

  tags = merge(local.common_tags, {
    Name = "Weekly Model Retrain Rule"
  })
}

# Lambda Layers for common dependencies
resource "aws_lambda_layer_version" "fleet_common_layer" {
  filename   = "${path.module}/lambda-layers/fleet-common-layer.zip"
  layer_name = "fleet-common-dependencies"

  compatible_runtimes = [var.lambda_runtime]
  description        = "Common dependencies for fleet management Lambda functions"

  # Create the layer zip file if it doesn't exist
  depends_on = [null_resource.create_lambda_layer]
}

resource "null_resource" "create_lambda_layer" {
  provisioner "local-exec" {
    command = <<EOF
mkdir -p ${path.module}/lambda-layers/python
pip install boto3 pandas numpy scikit-learn -t ${path.module}/lambda-layers/python/
cd ${path.module}/lambda-layers
zip -r fleet-common-layer.zip python/
EOF
  }

  triggers = {
    always_run = timestamp()
  }
}

# Lambda function aliases for blue-green deployments
resource "aws_lambda_alias" "telemetry_processor_live" {
  name             = "live"
  description      = "Live alias for telemetry processor"
  function_name    = aws_lambda_function.telemetry_processor.function_name
  function_version = "$LATEST"
}

resource "aws_lambda_alias" "predictive_maintenance_live" {
  name             = "live"
  description      = "Live alias for predictive maintenance"
  function_name    = aws_lambda_function.predictive_maintenance.function_name
  function_version = "$LATEST"
}

resource "aws_lambda_alias" "anomaly_detection_live" {
  name             = "live"
  description      = "Live alias for anomaly detection"
  function_name    = aws_lambda_function.anomaly_detection.function_name
  function_version = "$LATEST"
}
