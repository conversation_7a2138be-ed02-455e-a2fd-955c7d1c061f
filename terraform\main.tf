# Fleet Management Digital Twins - Terraform Configuration for AWS
# This configuration deploys the complete fleet management infrastructure

terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    archive = {
      source  = "hashicorp/archive"
      version = "~> 2.4"
    }
  }
}

provider "aws" {
  region = var.aws_region

  default_tags {
    tags = {
      Project     = "FleetManagement"
      Environment = var.environment
      ManagedBy   = "Terraform"
    }
  }
}

# Data sources
data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

# Local values
locals {
  account_id = data.aws_caller_identity.current.account_id
  region     = data.aws_region.current.name

  common_tags = {
    Project     = "FleetManagement"
    Environment = var.environment
    ManagedBy   = "Terraform"
  }

  # S3 bucket names (must be globally unique)
  twinmaker_bucket_name = "${var.project_name}-twinmaker-${local.account_id}"
  data_lake_bucket_name = "${var.project_name}-data-lake-${local.account_id}"
  lambda_bucket_name    = "${var.project_name}-lambda-${local.account_id}"
}

# S3 Buckets
resource "aws_s3_bucket" "twinmaker_workspace" {
  bucket = local.twinmaker_bucket_name

  tags = merge(local.common_tags, {
    Name = "TwinMaker Workspace Bucket"
  })
}

resource "aws_s3_bucket_versioning" "twinmaker_workspace" {
  bucket = aws_s3_bucket.twinmaker_workspace.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "twinmaker_workspace" {
  bucket = aws_s3_bucket.twinmaker_workspace.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

resource "aws_s3_bucket_public_access_block" "twinmaker_workspace" {
  bucket = aws_s3_bucket.twinmaker_workspace.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket" "data_lake" {
  bucket = local.data_lake_bucket_name

  tags = merge(local.common_tags, {
    Name = "Fleet Data Lake Bucket"
  })
}

resource "aws_s3_bucket_versioning" "data_lake" {
  bucket = aws_s3_bucket.data_lake.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "data_lake" {
  bucket = aws_s3_bucket.data_lake.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

resource "aws_s3_bucket_lifecycle_configuration" "data_lake" {
  bucket = aws_s3_bucket.data_lake.id

  rule {
    id     = "transition_to_ia"
    status = "Enabled"

    transition {
      days          = 30
      storage_class = "STANDARD_IA"
    }

    transition {
      days          = 90
      storage_class = "GLACIER"
    }

    transition {
      days          = 365
      storage_class = "DEEP_ARCHIVE"
    }
  }
}

resource "aws_s3_bucket_public_access_block" "data_lake" {
  bucket = aws_s3_bucket.data_lake.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# Lambda deployment bucket
resource "aws_s3_bucket" "lambda_deployments" {
  bucket = local.lambda_bucket_name

  tags = merge(local.common_tags, {
    Name = "Lambda Deployment Bucket"
  })
}

resource "aws_s3_bucket_server_side_encryption_configuration" "lambda_deployments" {
  bucket = aws_s3_bucket.lambda_deployments.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

resource "aws_s3_bucket_public_access_block" "lambda_deployments" {
  bucket = aws_s3_bucket.lambda_deployments.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# IoT SiteWise Asset Models
resource "aws_iotsitewise_asset_model" "fleet_vehicle_model" {
  name        = "FleetVehicleModel"
  description = "Asset model for fleet vehicles"

  # Vehicle Properties
  asset_model_properties {
    name      = "VehicleId"
    data_type = "STRING"
    type {
      attribute {
        default_value = ""
      }
    }
  }

  asset_model_properties {
    name      = "Make"
    data_type = "STRING"
    type {
      attribute {
        default_value = ""
      }
    }
  }

  asset_model_properties {
    name      = "Model"
    data_type = "STRING"
    type {
      attribute {
        default_value = ""
      }
    }
  }

  asset_model_properties {
    name      = "Year"
    data_type = "INTEGER"
    type {
      attribute {
        default_value = "2023"
      }
    }
  }

  # Telemetry Properties
  asset_model_properties {
    name      = "EngineTemperature"
    data_type = "DOUBLE"
    unit      = "Celsius"
    type {
      measurement {}
    }
  }

  asset_model_properties {
    name      = "OilPressure"
    data_type = "DOUBLE"
    unit      = "PSI"
    type {
      measurement {}
    }
  }

  asset_model_properties {
    name      = "RPM"
    data_type = "INTEGER"
    unit      = "RPM"
    type {
      measurement {}
    }
  }

  asset_model_properties {
    name      = "Speed"
    data_type = "DOUBLE"
    unit      = "km/h"
    type {
      measurement {}
    }
  }

  asset_model_properties {
    name      = "FuelLevel"
    data_type = "DOUBLE"
    unit      = "Percent"
    type {
      measurement {}
    }
  }

  asset_model_properties {
    name      = "BatteryVoltage"
    data_type = "DOUBLE"
    unit      = "Volts"
    type {
      measurement {}
    }
  }

  asset_model_properties {
    name      = "Odometer"
    data_type = "DOUBLE"
    unit      = "km"
    type {
      measurement {}
    }
  }

  # Calculated Metrics
  asset_model_properties {
    name      = "FuelEfficiency"
    data_type = "DOUBLE"
    unit      = "km/L"
    type {
      metric {
        expression = "avg(FuelLevel) / avg(Speed)"
        variables {
          name = "FuelLevel"
          value {
            property_id = aws_iotsitewise_asset_model.fleet_vehicle_model.asset_model_properties[4].id
          }
        }
        variables {
          name = "Speed"
          value {
            property_id = aws_iotsitewise_asset_model.fleet_vehicle_model.asset_model_properties[3].id
          }
        }
        window {
          tumbling {
            interval = "5m"
          }
        }
      }
    }
  }

  asset_model_properties {
    name      = "MaintenanceScore"
    data_type = "DOUBLE"
    unit      = "Percent"
    type {
      metric {
        expression = "100 - (EngineTemperature / 120 * 50 + (50 - OilPressure) / 50 * 50)"
        variables {
          name = "EngineTemperature"
          value {
            property_id = aws_iotsitewise_asset_model.fleet_vehicle_model.asset_model_properties[0].id
          }
        }
        variables {
          name = "OilPressure"
          value {
            property_id = aws_iotsitewise_asset_model.fleet_vehicle_model.asset_model_properties[1].id
          }
        }
        window {
          tumbling {
            interval = "1m"
          }
        }
      }
    }
  }

  tags = merge(local.common_tags, {
    Name = "Fleet Vehicle Asset Model"
  })
}

# IoT SiteWise Electric Vehicle Asset Model
resource "aws_iotsitewise_asset_model" "electric_vehicle_model" {
  name        = "ElectricVehicleModel"
  description = "Asset model for electric vehicles extending fleet vehicle model"

  # Inherit from Fleet Vehicle Model
  asset_model_hierarchies {
    name           = "FleetVehicleHierarchy"
    child_asset_model_id = aws_iotsitewise_asset_model.fleet_vehicle_model.id
  }

  # EV-specific Properties
  asset_model_properties {
    name      = "BatteryCapacity"
    data_type = "DOUBLE"
    unit      = "kWh"
    type {
      attribute {
        default_value = "75"
      }
    }
  }

  asset_model_properties {
    name      = "StateOfCharge"
    data_type = "DOUBLE"
    unit      = "Percent"
    type {
      measurement {}
    }
  }

  asset_model_properties {
    name      = "StateOfHealth"
    data_type = "DOUBLE"
    unit      = "Percent"
    type {
      measurement {}
    }
  }

  asset_model_properties {
    name      = "BatteryTemperature"
    data_type = "DOUBLE"
    unit      = "Celsius"
    type {
      measurement {}
    }
  }

  asset_model_properties {
    name      = "ChargingPower"
    data_type = "DOUBLE"
    unit      = "kW"
    type {
      measurement {}
    }
  }

  asset_model_properties {
    name      = "IsCharging"
    data_type = "BOOLEAN"
    type {
      measurement {}
    }
  }

  asset_model_properties {
    name      = "EstimatedRange"
    data_type = "DOUBLE"
    unit      = "km"
    type {
      measurement {}
    }
  }

  # EV-specific Metrics
  asset_model_properties {
    name      = "EnergyEfficiency"
    data_type = "DOUBLE"
    unit      = "kWh/100km"
    type {
      metric {
        expression = "(BatteryCapacity * (100 - StateOfCharge) / 100) / (Odometer / 100)"
        variables {
          name = "BatteryCapacity"
          value {
            property_id = aws_iotsitewise_asset_model.electric_vehicle_model.asset_model_properties[0].id
          }
        }
        variables {
          name = "StateOfCharge"
          value {
            property_id = aws_iotsitewise_asset_model.electric_vehicle_model.asset_model_properties[1].id
          }
        }
        variables {
          name = "Odometer"
          value {
            hierarchy_id = aws_iotsitewise_asset_model.electric_vehicle_model.asset_model_hierarchies[0].id
            property_id  = "OdometerPropertyId" # Reference to parent model property
          }
        }
        window {
          tumbling {
            interval = "15m"
          }
        }
      }
    }
  }

  asset_model_properties {
    name      = "BatteryHealthScore"
    data_type = "DOUBLE"
    unit      = "Percent"
    type {
      metric {
        expression = "StateOfHealth * (1 - abs(BatteryTemperature - 25) / 50)"
        variables {
          name = "StateOfHealth"
          value {
            property_id = aws_iotsitewise_asset_model.electric_vehicle_model.asset_model_properties[2].id
          }
        }
        variables {
          name = "BatteryTemperature"
          value {
            property_id = aws_iotsitewise_asset_model.electric_vehicle_model.asset_model_properties[3].id
          }
        }
        window {
          tumbling {
            interval = "5m"
          }
        }
      }
    }
  }

  tags = merge(local.common_tags, {
    Name = "Electric Vehicle Asset Model"
  })
}

# IoT SiteWise Charging Station Asset Model
resource "aws_iotsitewise_asset_model" "charging_station_model" {
  name        = "ChargingStationModel"
  description = "Asset model for EV charging stations"

  # Station Properties
  asset_model_properties {
    name      = "StationId"
    data_type = "STRING"
    type {
      attribute {
        default_value = ""
      }
    }
  }

  asset_model_properties {
    name      = "StationType"
    data_type = "STRING"
    type {
      attribute {
        default_value = "DC_FAST"
      }
    }
  }

  asset_model_properties {
    name      = "MaxPower"
    data_type = "DOUBLE"
    unit      = "kW"
    type {
      attribute {
        default_value = "150"
      }
    }
  }

  asset_model_properties {
    name      = "ConnectorCount"
    data_type = "INTEGER"
    type {
      attribute {
        default_value = "2"
      }
    }
  }

  # Operational Measurements
  asset_model_properties {
    name      = "CurrentPower"
    data_type = "DOUBLE"
    unit      = "kW"
    type {
      measurement {}
    }
  }

  asset_model_properties {
    name      = "AvailableConnectors"
    data_type = "INTEGER"
    type {
      measurement {}
    }
  }

  asset_model_properties {
    name      = "ActiveSessions"
    data_type = "INTEGER"
    type {
      measurement {}
    }
  }

  asset_model_properties {
    name      = "EnergyDelivered"
    data_type = "DOUBLE"
    unit      = "kWh"
    type {
      measurement {}
    }
  }

  asset_model_properties {
    name      = "GridVoltage"
    data_type = "DOUBLE"
    unit      = "Volts"
    type {
      measurement {}
    }
  }

  asset_model_properties {
    name      = "Temperature"
    data_type = "DOUBLE"
    unit      = "Celsius"
    type {
      measurement {}
    }
  }

  # Calculated Metrics
  asset_model_properties {
    name      = "UtilizationRate"
    data_type = "DOUBLE"
    unit      = "Percent"
    type {
      metric {
        expression = "(ActiveSessions / ConnectorCount) * 100"
        variables {
          name = "ActiveSessions"
          value {
            property_id = aws_iotsitewise_asset_model.charging_station_model.asset_model_properties[6].id
          }
        }
        variables {
          name = "ConnectorCount"
          value {
            property_id = aws_iotsitewise_asset_model.charging_station_model.asset_model_properties[3].id
          }
        }
        window {
          tumbling {
            interval = "1m"
          }
        }
      }
    }
  }

  asset_model_properties {
    name      = "PowerEfficiency"
    data_type = "DOUBLE"
    unit      = "Percent"
    type {
      metric {
        expression = "(CurrentPower / MaxPower) * 100"
        variables {
          name = "CurrentPower"
          value {
            property_id = aws_iotsitewise_asset_model.charging_station_model.asset_model_properties[4].id
          }
        }
        variables {
          name = "MaxPower"
          value {
            property_id = aws_iotsitewise_asset_model.charging_station_model.asset_model_properties[2].id
          }
        }
        window {
          tumbling {
            interval = "1m"
          }
        }
      }
    }
  }

  asset_model_properties {
    name      = "DailyEnergyDelivered"
    data_type = "DOUBLE"
    unit      = "kWh"
    type {
      metric {
        expression = "sum(EnergyDelivered)"
        variables {
          name = "EnergyDelivered"
          value {
            property_id = aws_iotsitewise_asset_model.charging_station_model.asset_model_properties[7].id
          }
        }
        window {
          tumbling {
            interval = "1d"
          }
        }
      }
    }
  }

  tags = merge(local.common_tags, {
    Name = "Charging Station Asset Model"
  })
}

# IoT SiteWise Fleet Asset Model (Root Level)
resource "aws_iotsitewise_asset_model" "fleet_model" {
  name        = "FleetModel"
  description = "Root asset model for fleet management"

  # Fleet Properties
  asset_model_properties {
    name      = "FleetId"
    data_type = "STRING"
    type {
      attribute {
        default_value = ""
      }
    }
  }

  asset_model_properties {
    name      = "FleetName"
    data_type = "STRING"
    type {
      attribute {
        default_value = ""
      }
    }
  }

  asset_model_properties {
    name      = "TotalVehicles"
    data_type = "INTEGER"
    type {
      attribute {
        default_value = "0"
      }
    }
  }

  # Fleet-level Metrics
  asset_model_properties {
    name      = "FleetUtilization"
    data_type = "DOUBLE"
    unit      = "Percent"
    type {
      metric {
        expression = "avg(VehicleUtilization)"
        variables {
          name = "VehicleUtilization"
          value {
            hierarchy_id = aws_iotsitewise_asset_model.fleet_model.asset_model_hierarchies[0].id
            property_id  = "UtilizationPropertyId"
          }
        }
        window {
          tumbling {
            interval = "1h"
          }
        }
      }
    }
  }

  asset_model_properties {
    name      = "AverageMaintenanceScore"
    data_type = "DOUBLE"
    unit      = "Percent"
    type {
      metric {
        expression = "avg(MaintenanceScore)"
        variables {
          name = "MaintenanceScore"
          value {
            hierarchy_id = aws_iotsitewise_asset_model.fleet_model.asset_model_hierarchies[0].id
            property_id  = "MaintenanceScorePropertyId"
          }
        }
        window {
          tumbling {
            interval = "15m"
          }
        }
      }
    }
  }

  # Hierarchies for child assets
  asset_model_hierarchies {
    name           = "VehicleHierarchy"
    child_asset_model_id = aws_iotsitewise_asset_model.fleet_vehicle_model.id
  }

  asset_model_hierarchies {
    name           = "ChargingStationHierarchy"
    child_asset_model_id = aws_iotsitewise_asset_model.charging_station_model.id
  }

  tags = merge(local.common_tags, {
    Name = "Fleet Asset Model"
  })
}

# SNS Topics for Alerts
resource "aws_sns_topic" "maintenance_alerts" {
  name = "fleet-maintenance-alerts"

  tags = merge(local.common_tags, {
    Name = "Maintenance Alerts Topic"
  })
}

resource "aws_sns_topic" "anomaly_alerts" {
  name = "fleet-anomaly-alerts"

  tags = merge(local.common_tags, {
    Name = "Anomaly Alerts Topic"
  })
}

# CloudWatch Log Groups
resource "aws_cloudwatch_log_group" "lambda_logs" {
  for_each = toset([
    "/aws/lambda/fleet-telemetry-processor",
    "/aws/lambda/fleet-predictive-maintenance",
    "/aws/lambda/fleet-anomaly-detection",
    "/aws/lambda/fleet-ev-charge-optimizer"
  ])

  name              = each.value
  retention_in_days = var.log_retention_days

  tags = merge(local.common_tags, {
    Name = "Lambda Log Group"
  })
}

# CloudWatch Dashboard
resource "aws_cloudwatch_dashboard" "fleet_management" {
  dashboard_name = "FleetManagementDashboard"

  dashboard_body = jsonencode({
    widgets = [
      {
        type   = "metric"
        x      = 0
        y      = 0
        width  = 12
        height = 6

        properties = {
          metrics = [
            ["AWS/Kinesis", "IncomingRecords", "StreamName", aws_kinesis_stream.vehicle_telemetry.name],
            ["AWS/Kinesis", "IncomingRecords", "StreamName", aws_kinesis_stream.driver_behavior.name],
            ["AWS/Kinesis", "IncomingRecords", "StreamName", aws_kinesis_stream.charging_data.name]
          ]
          view    = "timeSeries"
          stacked = false
          region  = local.region
          title   = "Kinesis Stream Incoming Records"
          period  = 300
        }
      },
      {
        type   = "metric"
        x      = 0
        y      = 6
        width  = 12
        height = 6

        properties = {
          metrics = [
            ["AWS/Lambda", "Invocations", "FunctionName", "fleet-telemetry-processor"],
            ["AWS/Lambda", "Invocations", "FunctionName", "fleet-predictive-maintenance"],
            ["AWS/Lambda", "Invocations", "FunctionName", "fleet-anomaly-detection"]
          ]
          view    = "timeSeries"
          stacked = false
          region  = local.region
          title   = "Lambda Function Invocations"
          period  = 300
        }
      }
    ]
  })
}
