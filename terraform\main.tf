# Fleet Management Digital Twins - Terraform Configuration for AWS
# This configuration deploys the complete fleet management infrastructure

terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    archive = {
      source  = "hashicorp/archive"
      version = "~> 2.4"
    }
  }
}

provider "aws" {
  region = var.aws_region
  
  default_tags {
    tags = {
      Project     = "FleetManagement"
      Environment = var.environment
      ManagedBy   = "Terraform"
    }
  }
}

# Data sources
data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

# Local values
locals {
  account_id = data.aws_caller_identity.current.account_id
  region     = data.aws_region.current.name
  
  common_tags = {
    Project     = "FleetManagement"
    Environment = var.environment
    ManagedBy   = "Terraform"
  }
  
  # S3 bucket names (must be globally unique)
  twinmaker_bucket_name = "${var.project_name}-twinmaker-${local.account_id}"
  data_lake_bucket_name = "${var.project_name}-data-lake-${local.account_id}"
  lambda_bucket_name    = "${var.project_name}-lambda-${local.account_id}"
}

# S3 Buckets
resource "aws_s3_bucket" "twinmaker_workspace" {
  bucket = local.twinmaker_bucket_name
  
  tags = merge(local.common_tags, {
    Name = "TwinMaker Workspace Bucket"
  })
}

resource "aws_s3_bucket_versioning" "twinmaker_workspace" {
  bucket = aws_s3_bucket.twinmaker_workspace.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "twinmaker_workspace" {
  bucket = aws_s3_bucket.twinmaker_workspace.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

resource "aws_s3_bucket_public_access_block" "twinmaker_workspace" {
  bucket = aws_s3_bucket.twinmaker_workspace.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket" "data_lake" {
  bucket = local.data_lake_bucket_name
  
  tags = merge(local.common_tags, {
    Name = "Fleet Data Lake Bucket"
  })
}

resource "aws_s3_bucket_versioning" "data_lake" {
  bucket = aws_s3_bucket.data_lake.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "data_lake" {
  bucket = aws_s3_bucket.data_lake.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

resource "aws_s3_bucket_lifecycle_configuration" "data_lake" {
  bucket = aws_s3_bucket.data_lake.id

  rule {
    id     = "transition_to_ia"
    status = "Enabled"

    transition {
      days          = 30
      storage_class = "STANDARD_IA"
    }

    transition {
      days          = 90
      storage_class = "GLACIER"
    }

    transition {
      days          = 365
      storage_class = "DEEP_ARCHIVE"
    }
  }
}

resource "aws_s3_bucket_public_access_block" "data_lake" {
  bucket = aws_s3_bucket.data_lake.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# Lambda deployment bucket
resource "aws_s3_bucket" "lambda_deployments" {
  bucket = local.lambda_bucket_name
  
  tags = merge(local.common_tags, {
    Name = "Lambda Deployment Bucket"
  })
}

resource "aws_s3_bucket_server_side_encryption_configuration" "lambda_deployments" {
  bucket = aws_s3_bucket.lambda_deployments.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

resource "aws_s3_bucket_public_access_block" "lambda_deployments" {
  bucket = aws_s3_bucket.lambda_deployments.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# Timestream Database
resource "aws_timestreamwrite_database" "fleet_management" {
  database_name = var.timestream_database_name
  
  tags = merge(local.common_tags, {
    Name = "Fleet Management Database"
  })
}

# Timestream Tables
resource "aws_timestreamwrite_table" "vehicle_telemetry" {
  database_name = aws_timestreamwrite_database.fleet_management.database_name
  table_name    = "VehicleTelemetry"

  retention_properties {
    memory_store_retention_period_in_hours  = 24
    magnetic_store_retention_period_in_days = 365
  }

  tags = merge(local.common_tags, {
    Name = "Vehicle Telemetry Table"
  })
}

resource "aws_timestreamwrite_table" "driver_behavior" {
  database_name = aws_timestreamwrite_database.fleet_management.database_name
  table_name    = "DriverBehavior"

  retention_properties {
    memory_store_retention_period_in_hours  = 24
    magnetic_store_retention_period_in_days = 365
  }

  tags = merge(local.common_tags, {
    Name = "Driver Behavior Table"
  })
}

resource "aws_timestreamwrite_table" "charging_data" {
  database_name = aws_timestreamwrite_database.fleet_management.database_name
  table_name    = "ChargingData"

  retention_properties {
    memory_store_retention_period_in_hours  = 24
    magnetic_store_retention_period_in_days = 365
  }

  tags = merge(local.common_tags, {
    Name = "Charging Data Table"
  })
}

# Kinesis Streams
resource "aws_kinesis_stream" "vehicle_telemetry" {
  name             = "fleet-vehicle-telemetry"
  shard_count      = var.kinesis_shard_count
  retention_period = 24

  encryption_type = "KMS"
  kms_key_id      = "alias/aws/kinesis"

  shard_level_metrics = [
    "IncomingRecords",
    "OutgoingRecords",
  ]

  tags = merge(local.common_tags, {
    Name = "Vehicle Telemetry Stream"
  })
}

resource "aws_kinesis_stream" "driver_behavior" {
  name             = "fleet-driver-behavior"
  shard_count      = 2
  retention_period = 24

  encryption_type = "KMS"
  kms_key_id      = "alias/aws/kinesis"

  tags = merge(local.common_tags, {
    Name = "Driver Behavior Stream"
  })
}

resource "aws_kinesis_stream" "charging_data" {
  name             = "fleet-charging-data"
  shard_count      = 2
  retention_period = 24

  encryption_type = "KMS"
  kms_key_id      = "alias/aws/kinesis"

  tags = merge(local.common_tags, {
    Name = "Charging Data Stream"
  })
}

# SNS Topics for Alerts
resource "aws_sns_topic" "maintenance_alerts" {
  name = "fleet-maintenance-alerts"
  
  tags = merge(local.common_tags, {
    Name = "Maintenance Alerts Topic"
  })
}

resource "aws_sns_topic" "anomaly_alerts" {
  name = "fleet-anomaly-alerts"
  
  tags = merge(local.common_tags, {
    Name = "Anomaly Alerts Topic"
  })
}

# CloudWatch Log Groups
resource "aws_cloudwatch_log_group" "lambda_logs" {
  for_each = toset([
    "/aws/lambda/fleet-telemetry-processor",
    "/aws/lambda/fleet-predictive-maintenance", 
    "/aws/lambda/fleet-anomaly-detection",
    "/aws/lambda/fleet-ev-charge-optimizer"
  ])
  
  name              = each.value
  retention_in_days = var.log_retention_days

  tags = merge(local.common_tags, {
    Name = "Lambda Log Group"
  })
}

# CloudWatch Dashboard
resource "aws_cloudwatch_dashboard" "fleet_management" {
  dashboard_name = "FleetManagementDashboard"

  dashboard_body = jsonencode({
    widgets = [
      {
        type   = "metric"
        x      = 0
        y      = 0
        width  = 12
        height = 6

        properties = {
          metrics = [
            ["AWS/Kinesis", "IncomingRecords", "StreamName", aws_kinesis_stream.vehicle_telemetry.name],
            ["AWS/Kinesis", "IncomingRecords", "StreamName", aws_kinesis_stream.driver_behavior.name],
            ["AWS/Kinesis", "IncomingRecords", "StreamName", aws_kinesis_stream.charging_data.name]
          ]
          view    = "timeSeries"
          stacked = false
          region  = local.region
          title   = "Kinesis Stream Incoming Records"
          period  = 300
        }
      },
      {
        type   = "metric"
        x      = 0
        y      = 6
        width  = 12
        height = 6

        properties = {
          metrics = [
            ["AWS/Lambda", "Invocations", "FunctionName", "fleet-telemetry-processor"],
            ["AWS/Lambda", "Invocations", "FunctionName", "fleet-predictive-maintenance"],
            ["AWS/Lambda", "Invocations", "FunctionName", "fleet-anomaly-detection"]
          ]
          view    = "timeSeries"
          stacked = false
          region  = local.region
          title   = "Lambda Function Invocations"
          period  = 300
        }
      }
    ]
  })
}
