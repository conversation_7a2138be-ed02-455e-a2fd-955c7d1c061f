# Outputs for Fleet Management Digital Twins Terraform Configuration

# S3 Bucket Outputs
output "twinmaker_bucket_name" {
  description = "Name of the S3 bucket for TwinMaker workspace"
  value       = aws_s3_bucket.twinmaker_workspace.bucket
}

output "twinmaker_bucket_arn" {
  description = "ARN of the S3 bucket for TwinMaker workspace"
  value       = aws_s3_bucket.twinmaker_workspace.arn
}

output "data_lake_bucket_name" {
  description = "Name of the S3 bucket for data lake"
  value       = aws_s3_bucket.data_lake.bucket
}

output "data_lake_bucket_arn" {
  description = "ARN of the S3 bucket for data lake"
  value       = aws_s3_bucket.data_lake.arn
}

output "lambda_bucket_name" {
  description = "Name of the S3 bucket for Lambda deployments"
  value       = aws_s3_bucket.lambda_deployments.bucket
}

# IoT SiteWise Outputs
output "sitewise_fleet_model_id" {
  description = "ID of the SiteWise fleet asset model"
  value       = aws_iotsitewise_asset_model.fleet_model.id
}

output "sitewise_vehicle_model_id" {
  description = "ID of the SiteWise vehicle asset model"
  value       = aws_iotsitewise_asset_model.fleet_vehicle_model.id
}

output "sitewise_ev_model_id" {
  description = "ID of the SiteWise electric vehicle asset model"
  value       = aws_iotsitewise_asset_model.electric_vehicle_model.id
}

output "sitewise_charging_station_model_id" {
  description = "ID of the SiteWise charging station asset model"
  value       = aws_iotsitewise_asset_model.charging_station_model.id
}

output "sitewise_gateway_id" {
  description = "ID of the SiteWise gateway"
  value       = var.enable_sitewise_edge_gateway ? aws_iotsitewise_gateway.fleet_gateway[0].id : null
}

output "sitewise_portal_id" {
  description = "ID of the SiteWise portal"
  value       = aws_iotsitewise_portal.fleet_portal.id
}

output "sitewise_project_id" {
  description = "ID of the SiteWise project"
  value       = aws_iotsitewise_project.fleet_project.id
}

# Data Processing Outputs
output "sitewise_portal_url" {
  description = "URL of the SiteWise portal"
  value       = "https://${aws_iotsitewise_portal.fleet_portal.portal_start_url}"
}

output "sitewise_dashboard_url" {
  description = "URL of the SiteWise dashboard"
  value       = "https://${aws_iotsitewise_portal.fleet_portal.portal_start_url}/dashboards/${aws_iotsitewise_dashboard.fleet_overview.id}"
}

# Lambda Function Outputs
output "telemetry_processor_function_name" {
  description = "Name of the telemetry processor Lambda function"
  value       = aws_lambda_function.telemetry_processor.function_name
}

output "telemetry_processor_function_arn" {
  description = "ARN of the telemetry processor Lambda function"
  value       = aws_lambda_function.telemetry_processor.arn
}

output "predictive_maintenance_function_name" {
  description = "Name of the predictive maintenance Lambda function"
  value       = aws_lambda_function.predictive_maintenance.function_name
}

output "predictive_maintenance_function_arn" {
  description = "ARN of the predictive maintenance Lambda function"
  value       = aws_lambda_function.predictive_maintenance.arn
}

output "anomaly_detection_function_name" {
  description = "Name of the anomaly detection Lambda function"
  value       = aws_lambda_function.anomaly_detection.function_name
}

output "anomaly_detection_function_arn" {
  description = "ARN of the anomaly detection Lambda function"
  value       = aws_lambda_function.anomaly_detection.arn
}

# IoT Outputs
output "iot_device_policy_name" {
  description = "Name of the IoT device policy"
  value       = aws_iot_policy.fleet_device_policy.name
}

output "fleet_vehicle_thing_type_name" {
  description = "Name of the fleet vehicle IoT thing type"
  value       = aws_iot_thing_type.fleet_vehicle.name
}

output "charging_station_thing_type_name" {
  description = "Name of the charging station IoT thing type"
  value       = aws_iot_thing_type.charging_station.name
}

output "fleet_vehicles_thing_group_name" {
  description = "Name of the fleet vehicles thing group"
  value       = aws_iot_thing_group.fleet_vehicles.name
}

# SNS Outputs
output "maintenance_alerts_topic_arn" {
  description = "ARN of the maintenance alerts SNS topic"
  value       = aws_sns_topic.maintenance_alerts.arn
}

output "anomaly_alerts_topic_arn" {
  description = "ARN of the anomaly alerts SNS topic"
  value       = aws_sns_topic.anomaly_alerts.arn
}

# IAM Role Outputs
output "twinmaker_execution_role_arn" {
  description = "ARN of the TwinMaker execution role"
  value       = aws_iam_role.twinmaker_execution_role.arn
}

output "lambda_execution_role_arn" {
  description = "ARN of the Lambda execution role"
  value       = aws_iam_role.lambda_execution_role.arn
}

output "sagemaker_execution_role_arn" {
  description = "ARN of the SageMaker execution role"
  value       = aws_iam_role.sagemaker_execution_role.arn
}

output "iot_rule_role_arn" {
  description = "ARN of the IoT rule execution role"
  value       = aws_iam_role.iot_rule_role.arn
}

# Data Catalog Outputs
output "glue_catalog_database_name" {
  description = "Name of the Glue catalog database"
  value       = aws_glue_catalog_database.fleet_data_catalog.name
}

output "athena_workgroup_name" {
  description = "Name of the Athena workgroup"
  value       = aws_athena_workgroup.fleet_analytics.name
}

# SageMaker Outputs (conditional)
output "sagemaker_domain_id" {
  description = "ID of the SageMaker domain"
  value       = var.enable_sagemaker ? aws_sagemaker_domain.fleet_ml_domain[0].id : null
}

output "sagemaker_endpoint_name" {
  description = "Name of the SageMaker predictive maintenance endpoint"
  value       = var.enable_sagemaker ? aws_sagemaker_endpoint.predictive_maintenance[0].name : null
}

output "sagemaker_model_package_group_name" {
  description = "Name of the SageMaker model package group"
  value       = var.enable_sagemaker ? aws_sagemaker_model_package_group.predictive_maintenance_group[0].model_package_group_name : null
}

# CloudWatch Outputs
output "cloudwatch_dashboard_name" {
  description = "Name of the CloudWatch dashboard"
  value       = aws_cloudwatch_dashboard.fleet_management.dashboard_name
}

# Kinesis Firehose Outputs
output "vehicle_telemetry_firehose_name" {
  description = "Name of the vehicle telemetry Firehose delivery stream"
  value       = aws_kinesis_firehose_delivery_stream.vehicle_telemetry_firehose.name
}

output "driver_behavior_firehose_name" {
  description = "Name of the driver behavior Firehose delivery stream"
  value       = aws_kinesis_firehose_delivery_stream.driver_behavior_firehose.name
}

output "charging_data_firehose_name" {
  description = "Name of the charging data Firehose delivery stream"
  value       = aws_kinesis_firehose_delivery_stream.charging_data_firehose.name
}

# Kinesis Analytics Outputs
output "kinesis_analytics_application_name" {
  description = "Name of the Kinesis Analytics application"
  value       = aws_kinesis_analytics_application.fleet_analytics.name
}

# Regional Information
output "aws_region" {
  description = "AWS region where resources are deployed"
  value       = local.region
}

output "aws_account_id" {
  description = "AWS account ID where resources are deployed"
  value       = local.account_id
}

# TwinMaker Configuration
output "twinmaker_workspace_name" {
  description = "Name of the TwinMaker workspace to be created"
  value       = var.twinmaker_workspace_name
}

# Deployment Information
output "deployment_timestamp" {
  description = "Timestamp of the deployment"
  value       = timestamp()
}

output "terraform_workspace" {
  description = "Terraform workspace used for deployment"
  value       = terraform.workspace
}

# Connection Endpoints
output "iot_endpoint" {
  description = "IoT Core endpoint for device connections"
  value       = "https://${data.aws_iot_endpoint.iot_endpoint.endpoint_address}"
}

output "timestream_endpoint" {
  description = "Timestream endpoint for data queries"
  value       = "https://query.timestream.${local.region}.amazonaws.com"
}

# Data source for IoT endpoint
data "aws_iot_endpoint" "iot_endpoint" {
  endpoint_type = "iot:Data-ATS"
}

# Summary Output
output "deployment_summary" {
  description = "Summary of deployed resources"
  value = {
    s3_buckets = {
      twinmaker_workspace = aws_s3_bucket.twinmaker_workspace.bucket
      data_lake          = aws_s3_bucket.data_lake.bucket
      lambda_deployments = aws_s3_bucket.lambda_deployments.bucket
    }
    timestream = {
      database = aws_timestreamwrite_database.fleet_management.database_name
      tables = {
        vehicle_telemetry = aws_timestreamwrite_table.vehicle_telemetry.table_name
        driver_behavior   = aws_timestreamwrite_table.driver_behavior.table_name
        charging_data     = aws_timestreamwrite_table.charging_data.table_name
      }
    }
    kinesis_streams = {
      vehicle_telemetry = aws_kinesis_stream.vehicle_telemetry.name
      driver_behavior   = aws_kinesis_stream.driver_behavior.name
      charging_data     = aws_kinesis_stream.charging_data.name
      analytics_output  = aws_kinesis_stream.analytics_output.name
    }
    lambda_functions = {
      telemetry_processor     = aws_lambda_function.telemetry_processor.function_name
      predictive_maintenance  = aws_lambda_function.predictive_maintenance.function_name
      anomaly_detection      = aws_lambda_function.anomaly_detection.function_name
    }
    iot_resources = {
      device_policy           = aws_iot_policy.fleet_device_policy.name
      fleet_vehicle_type      = aws_iot_thing_type.fleet_vehicle.name
      charging_station_type   = aws_iot_thing_type.charging_station.name
      fleet_vehicles_group    = aws_iot_thing_group.fleet_vehicles.name
    }
    sns_topics = {
      maintenance_alerts = aws_sns_topic.maintenance_alerts.arn
      anomaly_alerts     = aws_sns_topic.anomaly_alerts.arn
    }
    sagemaker = var.enable_sagemaker ? {
      domain_id    = aws_sagemaker_domain.fleet_ml_domain[0].id
      endpoint     = aws_sagemaker_endpoint.predictive_maintenance[0].name
      model_group  = aws_sagemaker_model_package_group.predictive_maintenance_group[0].model_package_group_name
    } : null
  }
}
