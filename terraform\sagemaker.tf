# SageMaker Resources for Fleet Management AI/ML

# SageMaker Model for Predictive Maintenance
resource "aws_sagemaker_model" "predictive_maintenance" {
  count = var.enable_sagemaker ? 1 : 0
  
  name               = "predictive-maintenance-model"
  execution_role_arn = aws_iam_role.sagemaker_execution_role.arn

  primary_container {
    image          = "763104351884.dkr.ecr.${local.region}.amazonaws.com/sklearn-inference:0.23-1-cpu-py3"
    model_data_url = "s3://${aws_s3_bucket.data_lake.bucket}/models/predictive-maintenance/model.tar.gz"
    environment = {
      SAGEMAKER_PROGRAM                = "inference.py"
      SAGEMAKER_SUBMIT_DIRECTORY       = "/opt/ml/code"
      SAGEMAKER_CONTAINER_LOG_LEVEL    = "20"
      SAGEMAKER_REGION                 = local.region
    }
  }

  tags = merge(local.common_tags, {
    Name = "Predictive Maintenance Model"
  })
}

# SageMaker Endpoint Configuration
resource "aws_sagemaker_endpoint_configuration" "predictive_maintenance" {
  count = var.enable_sagemaker ? 1 : 0
  
  name = "predictive-maintenance-config"

  production_variants {
    variant_name           = "primary"
    model_name            = aws_sagemaker_model.predictive_maintenance[0].name
    initial_instance_count = 1
    instance_type         = var.sagemaker_instance_type
    initial_variant_weight = 1.0
  }

  data_capture_config {
    enable_capture                = true
    initial_sampling_percentage   = 100
    destination_s3_uri           = "s3://${aws_s3_bucket.data_lake.bucket}/sagemaker-data-capture/"
    kms_key_id                   = "alias/aws/s3"

    capture_options {
      capture_mode = "Input"
    }

    capture_options {
      capture_mode = "Output"
    }

    capture_content_type_header {
      json_content_types = ["application/json"]
    }
  }

  tags = merge(local.common_tags, {
    Name = "Predictive Maintenance Endpoint Config"
  })
}

# SageMaker Endpoint
resource "aws_sagemaker_endpoint" "predictive_maintenance" {
  count = var.enable_sagemaker ? 1 : 0
  
  name                 = "predictive-maintenance-endpoint"
  endpoint_config_name = aws_sagemaker_endpoint_configuration.predictive_maintenance[0].name

  tags = merge(local.common_tags, {
    Name = "Predictive Maintenance Endpoint"
  })
}

# SageMaker Domain for Studio
resource "aws_sagemaker_domain" "fleet_ml_domain" {
  count = var.enable_sagemaker ? 1 : 0
  
  domain_name = "fleet-management-domain"
  auth_mode   = "IAM"
  vpc_id      = var.enable_vpc ? aws_vpc.main[0].id : null
  subnet_ids  = var.enable_vpc ? aws_subnet.private[*].id : null

  default_user_settings {
    execution_role = aws_iam_role.sagemaker_execution_role.arn

    jupyter_server_app_settings {
      default_resource_spec {
        instance_type       = "system"
        sagemaker_image_arn = "arn:aws:sagemaker:${local.region}:081325390199:image/datascience-1.0"
      }
    }

    kernel_gateway_app_settings {
      default_resource_spec {
        instance_type       = "ml.t3.medium"
        sagemaker_image_arn = "arn:aws:sagemaker:${local.region}:081325390199:image/datascience-1.0"
      }
    }

    tensor_board_app_settings {
      default_resource_spec {
        instance_type = "ml.t3.medium"
      }
    }
  }

  tags = merge(local.common_tags, {
    Name = "Fleet ML Domain"
  })
}

# SageMaker User Profile
resource "aws_sagemaker_user_profile" "fleet_data_scientist" {
  count = var.enable_sagemaker ? 1 : 0
  
  domain_id         = aws_sagemaker_domain.fleet_ml_domain[0].id
  user_profile_name = "fleet-data-scientist"

  user_settings {
    execution_role = aws_iam_role.sagemaker_execution_role.arn

    jupyter_server_app_settings {
      default_resource_spec {
        instance_type = "ml.t3.medium"
      }
    }
  }

  tags = merge(local.common_tags, {
    Name = "Fleet Data Scientist Profile"
  })
}

# SageMaker Model Registry
resource "aws_sagemaker_model_package_group" "predictive_maintenance_group" {
  count = var.enable_sagemaker ? 1 : 0
  
  model_package_group_name        = "predictive-maintenance-models"
  model_package_group_description = "Model package group for predictive maintenance models"

  tags = merge(local.common_tags, {
    Name = "Predictive Maintenance Model Group"
  })
}

# SageMaker Pipeline for Model Training
resource "aws_sagemaker_pipeline" "model_training_pipeline" {
  count = var.enable_sagemaker ? 1 : 0
  
  pipeline_name         = "fleet-model-training-pipeline"
  pipeline_display_name = "Fleet Model Training Pipeline"
  role_arn             = aws_iam_role.sagemaker_execution_role.arn

  pipeline_definition = jsonencode({
    Version = "2020-12-01"
    Metadata = {}
    Parameters = [
      {
        Name = "InputDataUrl"
        Type = "String"
        DefaultValue = "s3://${aws_s3_bucket.data_lake.bucket}/training-data/"
      },
      {
        Name = "ModelOutputPath"
        Type = "String"
        DefaultValue = "s3://${aws_s3_bucket.data_lake.bucket}/models/"
      }
    ]
    PipelineExperimentConfig = {
      ExperimentName = "fleet-predictive-maintenance"
      TrialName = "training-trial"
    }
    Steps = [
      {
        Name = "PreprocessData"
        Type = "Processing"
        Arguments = {
          ProcessingResources = {
            ClusterConfig = {
              InstanceType = "ml.m5.large"
              InstanceCount = 1
              VolumeSizeInGB = 30
            }
          }
          AppSpecification = {
            ImageUri = "763104351884.dkr.ecr.${local.region}.amazonaws.com/sklearn-inference:0.23-1-cpu-py3"
            ContainerEntrypoint = ["python3", "/opt/ml/processing/input/code/preprocess.py"]
          }
          RoleArn = aws_iam_role.sagemaker_execution_role.arn
          ProcessingInputs = [
            {
              InputName = "input-data"
              AppManaged = false
              S3Input = {
                S3Uri = "s3://${aws_s3_bucket.data_lake.bucket}/training-data/"
                LocalPath = "/opt/ml/processing/input/data"
                S3DataType = "S3Prefix"
                S3InputMode = "File"
              }
            },
            {
              InputName = "code"
              AppManaged = false
              S3Input = {
                S3Uri = "s3://${aws_s3_bucket.data_lake.bucket}/sagemaker-code/"
                LocalPath = "/opt/ml/processing/input/code"
                S3DataType = "S3Prefix"
                S3InputMode = "File"
              }
            }
          ]
          ProcessingOutputs = [
            {
              OutputName = "train"
              AppManaged = false
              S3Output = {
                S3Uri = "s3://${aws_s3_bucket.data_lake.bucket}/processed-data/train/"
                LocalPath = "/opt/ml/processing/output/train"
                S3UploadMode = "EndOfJob"
              }
            },
            {
              OutputName = "validation"
              AppManaged = false
              S3Output = {
                S3Uri = "s3://${aws_s3_bucket.data_lake.bucket}/processed-data/validation/"
                LocalPath = "/opt/ml/processing/output/validation"
                S3UploadMode = "EndOfJob"
              }
            }
          ]
        }
      },
      {
        Name = "TrainModel"
        Type = "Training"
        Arguments = {
          AlgorithmSpecification = {
            TrainingImage = "763104351884.dkr.ecr.${local.region}.amazonaws.com/sklearn-inference:0.23-1-cpu-py3"
            TrainingInputMode = "File"
          }
          RoleArn = aws_iam_role.sagemaker_execution_role.arn
          InputDataConfig = [
            {
              ChannelName = "train"
              DataSource = {
                S3DataSource = {
                  S3DataType = "S3Prefix"
                  S3Uri = "s3://${aws_s3_bucket.data_lake.bucket}/processed-data/train/"
                  S3DataDistributionType = "FullyReplicated"
                }
              }
            },
            {
              ChannelName = "validation"
              DataSource = {
                S3DataSource = {
                  S3DataType = "S3Prefix"
                  S3Uri = "s3://${aws_s3_bucket.data_lake.bucket}/processed-data/validation/"
                  S3DataDistributionType = "FullyReplicated"
                }
              }
            }
          ]
          OutputDataConfig = {
            S3OutputPath = "s3://${aws_s3_bucket.data_lake.bucket}/models/"
          }
          ResourceConfig = {
            InstanceType = "ml.m5.large"
            InstanceCount = 1
            VolumeSizeInGB = 30
          }
          StoppingCondition = {
            MaxRuntimeInSeconds = 3600
          }
          HyperParameters = {
            "n-estimators" = "100"
            "max-depth" = "10"
            "min-samples-split" = "5"
          }
        }
      },
      {
        Name = "EvaluateModel"
        Type = "Processing"
        Arguments = {
          ProcessingResources = {
            ClusterConfig = {
              InstanceType = "ml.m5.large"
              InstanceCount = 1
              VolumeSizeInGB = 30
            }
          }
          AppSpecification = {
            ImageUri = "763104351884.dkr.ecr.${local.region}.amazonaws.com/sklearn-inference:0.23-1-cpu-py3"
            ContainerEntrypoint = ["python3", "/opt/ml/processing/input/code/evaluate.py"]
          }
          RoleArn = aws_iam_role.sagemaker_execution_role.arn
          ProcessingInputs = [
            {
              InputName = "model"
              AppManaged = false
              S3Input = {
                S3Uri = "s3://${aws_s3_bucket.data_lake.bucket}/models/"
                LocalPath = "/opt/ml/processing/input/model"
                S3DataType = "S3Prefix"
                S3InputMode = "File"
              }
            },
            {
              InputName = "test-data"
              AppManaged = false
              S3Input = {
                S3Uri = "s3://${aws_s3_bucket.data_lake.bucket}/processed-data/validation/"
                LocalPath = "/opt/ml/processing/input/data"
                S3DataType = "S3Prefix"
                S3InputMode = "File"
              }
            }
          ]
          ProcessingOutputs = [
            {
              OutputName = "evaluation"
              AppManaged = false
              S3Output = {
                S3Uri = "s3://${aws_s3_bucket.data_lake.bucket}/evaluation/"
                LocalPath = "/opt/ml/processing/output/evaluation"
                S3UploadMode = "EndOfJob"
              }
            }
          ]
        }
      }
    ]
  })

  tags = merge(local.common_tags, {
    Name = "Model Training Pipeline"
  })
}

# SageMaker Model Monitor
resource "aws_sagemaker_data_quality_job_definition" "model_monitor" {
  count = var.enable_sagemaker ? 1 : 0
  
  job_definition_name = "predictive-maintenance-data-quality"
  role_arn           = aws_iam_role.sagemaker_execution_role.arn

  data_quality_app_specification {
    image_uri = "159807026194.dkr.ecr.${local.region}.amazonaws.com/sagemaker-model-monitor-analyzer"
  }

  data_quality_job_input {
    endpoint_input {
      endpoint_name                = aws_sagemaker_endpoint.predictive_maintenance[0].name
      local_path                   = "/opt/ml/processing/input/endpoint"
      s3_data_distribution_type    = "FullyReplicated"
      s3_input_mode               = "File"
    }
  }

  data_quality_job_output_config {
    monitoring_outputs {
      s3_output {
        s3_uri     = "s3://${aws_s3_bucket.data_lake.bucket}/model-monitor/data-quality/"
        local_path = "/opt/ml/processing/output"
      }
    }
  }

  job_resources {
    cluster_config {
      instance_count    = 1
      instance_type     = "ml.m5.large"
      volume_size_in_gb = 20
    }
  }

  stopping_condition {
    max_runtime_in_seconds = 3600
  }

  tags = merge(local.common_tags, {
    Name = "Model Data Quality Monitor"
  })
}

# SageMaker Monitoring Schedule
resource "aws_sagemaker_monitoring_schedule" "model_monitor_schedule" {
  count = var.enable_sagemaker ? 1 : 0
  
  monitoring_schedule_name = "predictive-maintenance-monitoring"

  monitoring_schedule_config {
    monitoring_job_definition_name = aws_sagemaker_data_quality_job_definition.model_monitor[0].job_definition_name
    monitoring_type               = "DataQuality"

    schedule_config {
      schedule_expression = "cron(0 */6 * * ? *)"  # Every 6 hours
    }
  }

  tags = merge(local.common_tags, {
    Name = "Model Monitoring Schedule"
  })
}
