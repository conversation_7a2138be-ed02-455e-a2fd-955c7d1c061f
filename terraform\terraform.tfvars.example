# Example Terraform Variables for Fleet Management Digital Twins
# Copy this file to terraform.tfvars and customize the values

# Basic Configuration
aws_region   = "us-east-1"
environment  = "dev"
project_name = "fleet-management"

# Timestream Configuration
timestream_database_name = "FleetManagementDB"

# Kinesis Configuration
kinesis_shard_count = 5

# Lambda Configuration
lambda_runtime     = "python3.9"
lambda_timeout     = 300
lambda_memory_size = 512
lambda_reserved_concurrency = 10

# Logging Configuration
log_retention_days = 14

# TwinMaker Configuration
twinmaker_workspace_name = "fleet-management-workspace"

# VPC Configuration (optional)
enable_vpc           = false
vpc_cidr            = "10.0.0.0/16"
private_subnet_cidrs = ["********/24", "********/24"]
public_subnet_cidrs  = ["**********/24", "**********/24"]

# SageMaker Configuration
enable_sagemaker        = true
sagemaker_instance_type = "ml.t2.medium"

# QuickSight Configuration
enable_quicksight = false

# Notification Configuration
notification_email = "<EMAIL>"

# IoT Thing Types Configuration
iot_thing_types = {
  FleetVehicle = {
    description = "Fleet vehicle IoT thing type"
    properties = {
      vehicleType = "string"
      make        = "string"
      model       = "string"
      year        = "number"
    }
  }
  ChargingStation = {
    description = "EV charging station IoT thing type"
    properties = {
      stationType = "string"
      maxPower    = "number"
      connectors  = "number"
    }
  }
}

# Kinesis Firehose Configuration
kinesis_firehose_buffer_size     = 5
kinesis_firehose_buffer_interval = 300

# Monitoring Configuration
enable_enhanced_monitoring = false
enable_x_ray_tracing      = false

# Backup Configuration
backup_retention_days      = 30
enable_cross_region_backup = false
backup_region             = "us-west-2"

# Security Configuration
enable_deletion_protection = true
enable_waf                = false

# API Gateway Configuration
api_gateway_stage_name     = "v1"
enable_api_gateway_logging = true

# Cost Allocation Tags
cost_allocation_tags = {
  Department = "Engineering"
  Team       = "IoT"
  CostCenter = "12345"
}
