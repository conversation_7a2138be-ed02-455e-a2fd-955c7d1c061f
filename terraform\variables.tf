# Variables for Fleet Management Digital Twins Terraform Configuration

variable "aws_region" {
  description = "AWS region for deployment"
  type        = string
  default     = "us-east-1"
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  default     = "dev"

  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be one of: dev, staging, prod."
  }
}

variable "project_name" {
  description = "Project name for resource naming"
  type        = string
  default     = "fleet-management"
}

variable "sitewise_gateway_name" {
  description = "Name for the IoT SiteWise gateway"
  type        = string
  default     = "fleet-management-gateway"
}

variable "sitewise_portal_name" {
  description = "Name for the IoT SiteWise portal"
  type        = string
  default     = "fleet-management-portal"
}

variable "enable_sitewise_edge_gateway" {
  description = "Whether to deploy SiteWise edge gateway with Greengrass"
  type        = bool
  default     = true
}

variable "log_retention_days" {
  description = "CloudWatch log retention period in days"
  type        = number
  default     = 14

  validation {
    condition = contains([
      1, 3, 5, 7, 14, 30, 60, 90, 120, 150, 180, 365, 400, 545, 731, 1827, 3653
    ], var.log_retention_days)
    error_message = "Log retention days must be a valid CloudWatch retention period."
  }
}

variable "twinmaker_workspace_name" {
  description = "Name for the IoT TwinMaker workspace"
  type        = string
  default     = "fleet-management-workspace"
}

variable "lambda_runtime" {
  description = "Lambda runtime version"
  type        = string
  default     = "python3.9"
}

variable "lambda_timeout" {
  description = "Lambda function timeout in seconds"
  type        = number
  default     = 300

  validation {
    condition     = var.lambda_timeout >= 1 && var.lambda_timeout <= 900
    error_message = "Lambda timeout must be between 1 and 900 seconds."
  }
}

variable "lambda_memory_size" {
  description = "Lambda function memory size in MB"
  type        = number
  default     = 512

  validation {
    condition     = var.lambda_memory_size >= 128 && var.lambda_memory_size <= 10240
    error_message = "Lambda memory size must be between 128 and 10240 MB."
  }
}

variable "enable_vpc" {
  description = "Whether to deploy resources in a VPC"
  type        = bool
  default     = false
}

variable "vpc_cidr" {
  description = "CIDR block for VPC (if enabled)"
  type        = string
  default     = "10.0.0.0/16"
}

variable "private_subnet_cidrs" {
  description = "CIDR blocks for private subnets (if VPC enabled)"
  type        = list(string)
  default     = ["********/24", "********/24"]
}

variable "public_subnet_cidrs" {
  description = "CIDR blocks for public subnets (if VPC enabled)"
  type        = list(string)
  default     = ["**********/24", "**********/24"]
}

variable "enable_sagemaker" {
  description = "Whether to deploy SageMaker resources"
  type        = bool
  default     = true
}

variable "sagemaker_instance_type" {
  description = "SageMaker endpoint instance type"
  type        = string
  default     = "ml.t2.medium"
}

variable "enable_quicksight" {
  description = "Whether to set up QuickSight resources"
  type        = bool
  default     = false
}

variable "notification_email" {
  description = "Email address for SNS notifications"
  type        = string
  default     = ""
}

variable "iot_thing_types" {
  description = "IoT thing types to create"
  type = map(object({
    description = string
    properties  = map(string)
  }))
  default = {
    FleetVehicle = {
      description = "Fleet vehicle IoT thing type"
      properties = {
        vehicleType = "string"
        make        = "string"
        model       = "string"
      }
    }
    ChargingStation = {
      description = "EV charging station IoT thing type"
      properties = {
        stationType = "string"
        maxPower    = "number"
        connectors  = "number"
      }
    }
  }
}

variable "sitewise_data_retention_days" {
  description = "Number of days to retain SiteWise data"
  type        = number
  default     = 365

  validation {
    condition     = var.sitewise_data_retention_days >= 1 && var.sitewise_data_retention_days <= 3653
    error_message = "SiteWise data retention must be between 1 and 3653 days."
  }
}

variable "sitewise_compute_location" {
  description = "Compute location for SiteWise metrics (EDGE or CLOUD)"
  type        = string
  default     = "CLOUD"

  validation {
    condition     = contains(["EDGE", "CLOUD"], var.sitewise_compute_location)
    error_message = "SiteWise compute location must be either EDGE or CLOUD."
  }
}

variable "enable_sitewise_alarms" {
  description = "Enable SiteWise alarms for monitoring"
  type        = bool
  default     = true
}

variable "enable_enhanced_monitoring" {
  description = "Enable enhanced monitoring for resources"
  type        = bool
  default     = false
}

variable "backup_retention_days" {
  description = "Number of days to retain backups"
  type        = number
  default     = 30
}

variable "enable_cross_region_backup" {
  description = "Enable cross-region backup replication"
  type        = bool
  default     = false
}

variable "backup_region" {
  description = "Region for cross-region backup replication"
  type        = string
  default     = "us-west-2"
}

variable "cost_allocation_tags" {
  description = "Additional tags for cost allocation"
  type        = map(string)
  default     = {}
}

variable "enable_deletion_protection" {
  description = "Enable deletion protection for critical resources"
  type        = bool
  default     = true
}

variable "lambda_reserved_concurrency" {
  description = "Reserved concurrency for Lambda functions"
  type        = number
  default     = 10

  validation {
    condition     = var.lambda_reserved_concurrency >= 0 && var.lambda_reserved_concurrency <= 1000
    error_message = "Lambda reserved concurrency must be between 0 and 1000."
  }
}

variable "enable_x_ray_tracing" {
  description = "Enable X-Ray tracing for Lambda functions"
  type        = bool
  default     = false
}

variable "api_gateway_stage_name" {
  description = "API Gateway stage name"
  type        = string
  default     = "v1"
}

variable "enable_api_gateway_logging" {
  description = "Enable API Gateway access logging"
  type        = bool
  default     = true
}

variable "enable_waf" {
  description = "Enable AWS WAF for API Gateway"
  type        = bool
  default     = false
}
